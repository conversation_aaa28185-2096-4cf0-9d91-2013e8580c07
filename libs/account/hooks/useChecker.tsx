import type { FC, PropsWithChildren } from 'preact/compat';
import { useCallback, useEffect, useState } from 'preact/compat';

import checkerIcon from '../assets/icon-checker.svg';
import checkerActiveIcon from '../assets/icon-checker-active.svg';

/**
 * checkbox 验证
 *
 * 用于同意协议等该类型选择封装
 * 返回 check 状态与组件
 *
 */

function useChecker(
  defaultValue?: boolean,
  callback?: (val: boolean) => void,
): [boolean, FC<PropsWithChildren>, (val: boolean) => void] {
  const [status, setStatus] = useState(defaultValue || false);

  const handleClick = useCallback(() => {
    setStatus(prev => !prev);
  }, []);

  const changeValue = useCallback((val: boolean) => {
    setStatus(val);
  }, []);

  // status 变动时调用一次回调
  useEffect(() => {
    callback?.(status);
  }, [callback, status]);

  const Component: FC<PropsWithChildren> = useCallback(
    ({ children }) => (
      <div className="group flex text-caption text-body cursor-pointer items-center" onClick={handleClick}>
        <div className="flex justify-center transform">
          <span className="min-w-[15px] mr-sp4">
            <img
              alt="check_icon"
              className="opacity-80 group-hover:opacity-100"
              src={status ? checkerActiveIcon : checkerIcon}
              width={15}
              height={15}
            />
          </span>
        </div>
        <div className={status ? 'text-white-100' : 'text-white-60 group-hover:text-white-100'}>{children}</div>
      </div>
    ),
    [handleClick, status],
  );

  return [status, Component, changeValue];
}

export default useChecker;

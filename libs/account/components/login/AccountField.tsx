import type { ChangeEvent, ForwardedRef, HTMLAttributes } from 'preact/compat';
import { clsx } from 'clsx';
import { forwardRef, useCallback, useEffect, useRef, useState } from 'preact/compat';
import { useClickOutside } from '@huse/click-outside';
import { Icon, type TIcon } from '@ssglauncher/iconfont';
import { TooltipContent } from '@ssglauncher/components';
import FormErrMsg from '../FormErrMsg';
import AccountDropdown from './AccountDropdown';

export interface AccountFieldProps extends Omit<HTMLAttributes<HTMLInputElement>, 'onSelect'> {
  /** 左侧图标 */
  icon?: TIcon;
  /** 是否显示清除按钮 */
  clearable?: boolean;
  /** 错误信息 */
  error?: string;
  /** 错误信息是否显示 */
  errorVisible?: boolean;
  /** 最近使用过的历史账号 */
  historyAccounts?: string[];
  placeholder?: string;
  /** 响应删除使用过的历史账号 */
  onDeleteHistory?: (index: number) => () => void;
  /** 响应清除按钮 */
  onClear?: () => void;
  /** 响应下拉选择回调 */
  onSelect?: (val: string) => void;
  onBlur?: (e: any) => any;
  /** 触发表单校验，目前在输入框失焦或选中下拉选项后进行校验 */
  trigger?: () => void;
  /** 是否展示输入框上方的tooltips提示  */
  showTips?: boolean;
  /** 输入框上方的tooltips提示内容 */
  tips?: string;
}

/**
 * 登录账号输入框
 *
 */
export function AccountField(
  {
    value,
    maxLength = 20,
    icon = 'mailbox',
    error,
    errorVisible = true,
    clearable,
    historyAccounts = [],
    placeholder,
    showTips,
    tips,
    onDeleteHistory,
    onChange,
    onSelect,
    onClear,
    trigger,
    ...rest
  }: AccountFieldProps,
  ref: ForwardedRef<HTMLInputElement>,
) {
  const [showDropdown, setDropdown] = useState(false);
  const [isHover, setIsHover] = useState(false);
  const [isFocus, setIsFocus] = useState(false);
  const handleBlur = () => {
    setIsFocus(false);
    trigger?.();
  };
  const handleFocus = () => {
    setTimeout(() => {
      // 解决失焦时清除按钮也可响应的问题
      setIsFocus(true);
    }, 0);
  };

  const accountList = historyAccounts;

  const handleSelect = (val: ChangeEvent<HTMLInputElement> & string) => {
    setDropdown(false);
    onChange?.(val);
    onSelect?.(val);
    trigger?.();
  };

  const handleDelete = (index: number) => {
    const callbackDelete = onDeleteHistory?.(index);
    callbackDelete?.();
  };

  const handleClear = useCallback(() => {
    if (!isFocus) {
      return;
    }
    onClear?.();
  }, [isFocus, onClear]);

  // value有值时不显示dropdown
  useEffect(() => {
    if (value) {
      setDropdown(false);
    }
  }, [value]);

  // // 点击外面收起
  const nodeRef = useRef();
  if (!import.meta.env.SSR) {
    useClickOutside(nodeRef, () => {
      setDropdown(false);
    });
  }

  useEffect(() => {
    let input = document.getElementsByName('account');
    if (input?.[0]) {
      input[0].placeholder = placeholder;
    }
  }, [placeholder]);

  return (
    <div>
      <div
        className={clsx('relative flex items-center justify-between rounded-medium bg-black-88')}
        onMouseEnter={() => setIsHover(true)}
        onMouseLeave={() => setIsHover(false)}
      >
        <label
          className={clsx(
            'flex-1 flex items-center px-sp12 py-[11px] border-transparent border rounded-medium focus-within:border-brand-default cursor-text',
            {
              '!border-error': error,
            },
            showDropdown && 'border-brand-default',
          )}
        >
          <Icon
            icon={icon}
            className={clsx('mr-sp8 text-white-20 text-title', (isHover || isFocus) && 'text-white-80')}
          />
          <input
            {...rest}
            ref={ref}
            value={value}
            onInput={onChange}
            className={clsx(
              'text-head outline-none w-full bg-transparent placeholder:text-white-20',
              isFocus ? 'text-white-100' : 'text-white-80',
            )}
            maxLength={maxLength}
            // placeholder={placeholder}
            spellCheck={false}
            onFocus={handleFocus}
            onBlur={handleBlur}
          />
          {clearable && value !== '' && isFocus && (
            <div
              className="w-16px h-16px bg-white-8 rounded-full flex items-center justify-center cursor-pointer mr-6px shrink-0 z-99"
              onMouseDown={handleClear}
            >
              <Icon icon="close" className={clsx('text-tag hover:text-white-100 hover:bg-white-12')} />
            </div>
          )}
          {!!accountList.length && (
            <div
              className="flex items-center justify-center cursor-pointer shrink-0"
              onClick={() => setDropdown(v => !v)}
            >
              <Icon
                icon="below"
                className={clsx(
                  'text-body hover:text-white-80',
                  showDropdown || isFocus ? 'text-white-100' : 'text-white-40',
                )}
              />
            </div>
          )}
        </label>
        {showTips && isFocus && <TooltipContent className="absolute top-[-36px]" content={tips || ''} />}
        {showDropdown && (
          <div ref={nodeRef} className="w-full absolute top-44px z-50">
            <AccountDropdown icon={icon} list={accountList} onSelect={handleSelect} onDelete={handleDelete} />
          </div>
        )}
      </div>
      {errorVisible && <FormErrMsg error={error || ''} />}
    </div>
  );
}

export default forwardRef<HTMLInputElement, AccountFieldProps>(AccountField);

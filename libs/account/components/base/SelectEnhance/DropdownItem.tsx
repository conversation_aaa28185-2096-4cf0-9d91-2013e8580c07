import type { TIcon } from '@ssglauncher/iconfont';
import { Icon } from '@ssglauncher/iconfont';

interface DropdownItemProps {
  title: string;
  icon?: TIcon;
  showClose?: boolean;
  onClose?: () => void;
  onClick?: () => void;
}

function DropdownItem({ title, icon, showClose, onClose, onClick }: DropdownItemProps) {
  const handleClose = (e: Event) => {
    e.stopPropagation();
    onClose?.();
  };
  return (
    <div
      className="py-[10px] px-sp12 flex items-center justify-between text-white-80 bg-white-8 hover:bg-white-20 hover:text-white-100 cursor-pointer"
      onClick={onClick}
    >
      {icon && <Icon icon={icon} className="mr-sp8" />}
      <span className="w-full text-head" title={title}>
        {title}
      </span>
      {showClose && (
        <div className="group min-w-sp16 w-sp16 h-sp16 rounded-full bg-white-4 hover:bg-white-8 flex items-center justify-center">
          <Icon icon="close" className="text-[10px] group-hover:text-white-100" onClick={handleClose} />
        </div>
      )}
    </div>
  );
}

export default DropdownItem;

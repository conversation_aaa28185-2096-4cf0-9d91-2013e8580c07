import type { I<PERSON>aptch<PERSON> } from './captcha.dto';
import { type ZodType } from 'zod';
import * as z from 'zod';
import { EVerifyType } from '../../model/verifyType.enum';
import { CaptchaDTO, captchaSchema } from './captcha.dto';
import { ABaseDTO } from '../../base/dto';

export const riskCodeSchema = z.object({
  /** 极验 */
  captcha: captchaSchema.optional(),
  /** 流水号 */
  serialNumber: z.string().nonempty(),
  /** 验证类型 */
  verifyType: z.nativeEnum(EVerifyType),
  /** 海外流水号(serialNumber) */
  ticket: z.string().optional(),
});

export type IRiskCode = z.infer<typeof riskCodeSchema>;

/**
 * 获取风控验证码 DTO
 */

export class RiskCodeDTO extends ABaseDTO implements IRiskCode {
  static of(data: IRiskCode): RiskCodeDTO {
    return new RiskCodeDTO(data);
  }

  serialNumber: string;
  verifyType: EVerifyType;
  captcha?: ICaptcha;
  ticket?: string;

  constructor(data: IRiskCode) {
    super();

    this.serialNumber = data.serialNumber;
    this.verifyType = data.verifyType;
    this.captcha = data.captcha;
    this.ticket = data.ticket;
  }

  get schema(): ZodType<IRiskCode> {
    return riskCodeSchema;
  }
}

// unit test
if (import.meta.vitest) {
  const { expect, test } = import.meta.vitest;

  test('of', () => {
    const data: IRiskCode = {
      serialNumber: '123',
      verifyType: EVerifyType.SMS,
      captcha: CaptchaDTO.of({
        captchaOutput: '123',
        genTime: '123',
        lotNumber: '123',
        passToken: '123',
      }),
    };

    const dto = RiskCodeDTO.of(data);

    expect(dto).toBeInstanceOf(RiskCodeDTO);

    expect(dto.serialNumber).toBe(data.serialNumber);
    expect(dto.verifyType).toBe(data.verifyType);
    expect(dto.captcha).toEqual(data.captcha);
  });

  test('验证数据（正常情况）', async () => {
    const data: IRiskCode = {
      serialNumber: '123',
      verifyType: EVerifyType.SMS,
      captcha: await CaptchaDTO.of({
        captchaOutput: '123',
        genTime: '123',
        lotNumber: '123',
        passToken: '123',
      }).validate(),
    };

    const validDto = RiskCodeDTO.of(data);
    await expect(validDto.validate()).resolves.not.toThrow();
    await expect(validDto.validate()).resolves.toStrictEqual(data);
  });

  test('缺斤少两', async () => {
    const data: IRiskCode = {
      serialNumber: '123',
      // verifyType: EVerifyType.SMS,
      captcha: CaptchaDTO.of({
        captchaOutput: '123',
        genTime: '123',
        lotNumber: '123',
        passToken: '123',
      }),
    } as unknown as IRiskCode;

    const validDto = RiskCodeDTO.of(data);
    await expect(validDto.validate()).rejects.toThrow(z.ZodError);
  });

  test('类型不符合要求', async () => {
    const data: IRiskCode = {
      serialNumber: 123,
      verifyType: EVerifyType.SMS,
      captcha: CaptchaDTO.of({
        captchaOutput: '123',
        genTime: '123',
        lotNumber: '123',
        passToken: '123',
      }),
    } as unknown as IRiskCode;

    const validDto = RiskCodeDTO.of(data);
    await expect(validDto.validate()).rejects.toThrow(z.ZodError);
  });

  test('内容不符合要求', async () => {
    const data: IRiskCode = {
      serialNumber: '123',
      verifyType: 'abc',
      captcha: CaptchaDTO.of({
        captchaOutput: '123',
        genTime: '123',
        lotNumber: '123',
        passToken: '123',
      }),
    } as unknown as IRiskCode;

    const validDto = RiskCodeDTO.of(data);
    await expect(validDto.validate()).rejects.toThrow(z.ZodError);
  });
}

import { compareIndex, parentIndexOf } from '../utils';
import { type NavElement, type NavIndex, querySelectorAll, querySelector, isNavigable } from './elements';

/**
 * 导航节点
 */
export interface NavNode {
  el: NavElement;
  children: NavNode[];
  prev: NavNode | null;
  next: NavNode | null;
}

function toNavNode(el: NavElement): NavNode {
  return {
    el,
    children: [],
    prev: null,
    next: null,
  };
}

/**
 * 查询导航节点树
 * @param group 分组索引
 * @param query 节点查询条件
 *  - int - 分组内节点下标 范围：`[-len+1, len-1]`
 *  - NavIndex - 节点索引字符串
 *  - 'asDefault' - 默认节点
 */
export function findNavNode(group: string, query: number | NavIndex | 'asDefault' = 'asDefault'): NavNode | null {
  const elements = querySelectorAll(`[data-group="${group}"]`).filter(isNavigable);
  if (elements.length === 0) return null;

  let i: number;
  if (query === 'asDefault') {
    i = elements.findIndex(el => el.dataset.asDefault === 'true');
    if (i === -1) i = 0; // first as default
  } else if (typeof query === 'string') {
    i = elements.findIndex(el => el.dataset.index === query);
  } else {
    i = query < 0 ? elements.length + query : query;
  }

  const el = elements[i];
  if (!el) return null;

  const node = toNavNode(el);
  if (i > 0) {
    node.prev = toNavNode(elements[i - 1]);
  }
  if (i + 1 < elements.length) {
    node.next = toNavNode(elements[i + 1]);
  }
  node.children = querySelectorAll(`[data-group="${el.dataset.index}"]`).filter(isNavigable).map(toNavNode);

  return node;
}

export function findNavNodeByIndex(index: string) {
  const el = querySelector(`[data-index="${index}"]`);
  if (!el || !isNavigable(el)) return null;
  return toNavNode(el);
}

export function siblingNavNodes(group: string) {
  const elements = querySelectorAll(`[data-group="${group}"]`).filter(isNavigable);
  return elements.map(toNavNode);
}

function isSibling(aIndex: string, bIndex: string) {
  return parentIndexOf(aIndex) === parentIndexOf(bIndex);
}

function findAllGroups(): string[] {
  return Array.from(
    new Set(
      querySelectorAll('[data-group]')
        .filter(isNavigable)
        .map(el => el.dataset.group),
    ),
  ).sort(compareIndex);
}

function siblingGroup(group: string, offset: number) {
  const groups = findAllGroups().filter(it => isSibling(it, group));
  const i = groups.indexOf(group);
  return groups[i + offset];
}

/** 查询上个兄弟分组的默认节点 */
export function prevGroupNavNode(group: string) {
  const prevGroup = siblingGroup(group, -1);
  if (!prevGroup) return null;
  return findNavNode(prevGroup);
}

/** 查询下个兄弟分组的默认节点 */
export function nextGroupNavNode(group: string) {
  const nextGroup = siblingGroup(group, 1);
  if (!nextGroup) return null;
  return findNavNode(nextGroup);
}

/** 查询兄弟分组的子(默认)节点 */
export function nextGroupChildNavNode(group: string) {
  const prefix = parentIndexOf(group) || group;
  const prefixReg = new RegExp(`^${prefix}-\\d$`);
  const groups = findAllGroups().filter(it => prefixReg.test(it) && it !== group);
  const subGroup = groups[0];
  if (!subGroup) return null;
  return findNavNode(subGroup);
}

/** 查询下个父级分组(默认)节点 */
export function nextParentGroupNavNode(group: string) {
  const parentGroup = parentIndexOf(group);
  if (!parentGroup) return null;
  return nextGroupNavNode(parentGroup);
}

/** 查询下个导航节点 */
export function findNextNavNode(group: string, allowSubGroup?: boolean) {
  // 若允许，优先查询兄弟分组的子节点
  // 查询兄弟分组的节点
  // 其次，查询父级分组的节点
  return (
    (allowSubGroup && nextGroupChildNavNode(group)) || nextGroupNavNode(group) || nextParentGroupNavNode(group) || null
  );
}

import { logger } from '../../utils';
import {
  getActiveElement,
  isNavElement,
  isVisible,
  querySelector,
  type NavElement,
  getDefaultNavElement,
} from '../elements';

/** 当前导航元素 */
let activeNavElement: NavElement | null = null;

export const getActiveNavElement = () => activeNavElement;

export function activateNavElement(shouldReactivate: boolean): NavElement | null {
  const activeElement = getActiveElement();

  // 优先获取当前焦点元素
  if (isNavElement(activeElement)) {
    if (FocusStyle.hasStyle(activeElement)) {
      logger.tag('activateNavElement').log('焦点元素已激活', activeElement.dataset.index);
      return activeElement;
    }

    logger.tag('activateNavElement').log('焦点元素重新激活', activeElement.dataset.index);
    focus(activeElement);
    return null;
  }

  if (isNavElement(activeNavElement) && !activeNavElement.isConnected && shouldReactivate) {
    const { index, group } = activeNavElement.dataset;
    const el = querySelector(`[data-index="${index}"]`);
    if (el) {
      logger.tag('activateNavElement').log('刷新文档中的当前导航元素', el.dataset.index);
      focus(el);
      return null;
    }

    if (group.includes('-')) {
      const parent = querySelector(`[data-index="${group}"]`);
      if (parent) {
        logger.tag('activateNavElement').log('激活当前导航元素父节点', parent.dataset.index);
        focus(parent);
        return null;
      }
    }
  }

  if (!isNavElement(activeNavElement) || !isVisible(activeNavElement)) {
    if (shouldReactivate) {
      const el = getDefaultNavElement();
      logger.tag('activateNavElement').log('激活默认导航元素', el?.dataset.index);
      focus(el);
    }
    return null;
  }

  if (activeNavElement !== activeElement && shouldReactivate) {
    logger.tag('activateNavElement').log('重新激活当前导航元素', activeNavElement.dataset.index);
    focus(activeNavElement);
    return null;
  }

  return shouldReactivate ? activeNavElement : null;
}

/** 焦点样式管理 */
export const FocusStyle = {
  className: 'is-focus',
  hasCls(el: NavElement) {
    return el.classList.contains(this.className);
  },
  hasStyle(el: NavElement) {
    return this.hasCls(el) || !!window.getComputedStyle(el).getPropertyValue('--is-focus');
  },
  addCls(el: NavElement) {
    if (!this.hasCls(el)) {
      el.classList.add(this.className);
    }
  },
  removeCls(el: NavElement) {
    if (this.hasCls(el)) {
      el.classList.remove(this.className);
    }
  },
};

export function focus(el: NavElement | null | undefined, scrollIntoView = true) {
  if (!el) return;

  el.focus();
  FocusStyle.addCls(el);
  el.addEventListener(
    'blur',
    () => {
      FocusStyle.removeCls(el);
    },
    { once: true },
  );
  if (activeNavElement && activeNavElement !== el) {
    FocusStyle.removeCls(activeNavElement);
  }
  if (scrollIntoView) {
    el.scrollIntoView({ behavior: 'smooth', block: 'center', inline: 'center' });
  }

  activeNavElement = el;

  logger('focus', el.dataset.index);
}

export function blur(el: NavElement) {
  el.blur();
  FocusStyle.removeCls(el);
  logger('blur', el.dataset.index);
}

export function click(el: NavElement) {
  el.click();

  // 处理点击事件引发组件更新导致焦点丢失情况
  setTimeout(() => {
    if (!FocusStyle.hasCls(el)) {
      focus(el, false);
    }
  }, 0);

  logger('click', el.dataset.index);
}

import type { Variant } from 'unocss';
import type { Theme } from '../theme';

/**
 * Variant `vh-{breakpoint}` for applying parent selectors based on viewport height breakpoints.
 */
export function variantHeightBreakpoints(): Variant<Theme> {
  const preOrder = 4000;

  return {
    name: 'heightBreakpoints',
    match(matcher, ctx) {
      const breakpoints = ctx.theme.heightBreakpoints || {};
      const match = matcher.match(/^vh-(.+):(.+)$/);
      if (!match) return;
      const [, bp, m] = match;
      const size = /^\d+px$/.test(bp) ? bp : breakpoints[bp];
      if (!size) return;
      const order = parseInt(size, 10);
      return {
        matcher: m,
        handle: (input, next) =>
          next({
            ...input,
            parent: `${input.parent ? `${input.parent} $$ ` : ''}@media (min-height: ${size})`,
            parentOrder: preOrder + order, // ASC order
          }),
      };
    },
    multiPass: true,
    autocomplete: 'vh-$heightBreakpoints:',
  };
}

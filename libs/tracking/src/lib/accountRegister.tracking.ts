import { BaseTracking } from './base.tracking';

export class AccountRegisterTracking extends BaseTracking {
  /**
   * 注册方式选择
   */
  ssgChooseRegister(ext: {
    /**
     * 用户输入的账号信息
     */
    preAccountId: string;
  }) {
    this.report({
      key: 'ssgChooseRegister',
      desc: '注册方式选择',
      ext,
    });
  }

  /**
   * 注册账号
   */
  ssgRegisterAccountSubmitted(ext: {
    /**
     * 注册方式选择：手机注册、普通注册、m1账号注册
     */
    regWay: '普通注册' | '手机注册' | 'm1账号注册';
    /**
     * 用户输入的账号信息
     */
    preAccountId: string;
    /**
     * 手机号
     */
    phoneNumber: string;
    /**
     * 是否同意协议：1-是，0-否
     */
    isAgreeDeal: '0' | '1';
  }) {
    this.report({
      key: 'ssgRegisterAccountSubmitted',
      desc: '注册账号提交',
      ext,
    });
  }

  /**
   * 注册账号成功
   */
  ssgRegisterAccountSuccess(ext: {
    /**
     * 注册方式选择：手机注册、普通注册、m1账号注册
     */
    regWay: '普通注册' | '手机注册' | 'm1账号注册';
    /**
     * 用户输入的账号信息
     */
    preAccountId: string;
    /**
     * 手机号
     */
    phoneNumber: string;
  }) {
    this.report({
      key: 'ssgRegisterAccountSuccess',
      desc: '注册账号成功',
      ext,
    });
  }

  /**
   * 注册账号失败
   */
  ssgRegisterAccountFail(ext: {
    /**
     * 注册方式选择：手机注册、普通注册、m1账号注册
     */
    regWay: '普通注册' | '手机注册' | 'm1账号注册';
    /**
     * 用户输入的账号信息
     */
    preAccountId: string;
    /**
     * 手机号
     */
    phoneNumber: string;
    /**
     * 失败原因，枚举值
     */
    code: number;
    /**
     * 失败原因：未同意协议、账号或密码错误、验证码错误、请求超时、实名信息无法匹配等等
     */
    statusReason: string;
  }) {
    this.report({
      key: 'ssgRegisterAccountFail',
      desc: '注册账号失败',
      ext,
    });
  }

  /**
   * 注册账号-实名认证
   */
  ssgRegisterAccountCertify(ext: {
    /**
     * 注册方式选择：手机注册、普通注册、m1账号注册
     */
    regWay: '普通注册' | '手机注册' | 'm1账号注册';
    /**
     * 用户输入的账号信息
     */
    preAccountId: string;
    /**
     * 手机号
     */
    phoneNumber: string;
  }) {
    this.report({
      key: 'ssgRegisterAccountCertify',
      desc: '注册账号-实名认证',
      ext,
    });
  }
}

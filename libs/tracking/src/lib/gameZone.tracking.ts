import { BaseTracking } from './base.tracking';

export class GameZoneTracking extends BaseTracking {
  /**
   * 点击进入游戏专区页
   */
  ssgClickIntozone(ext: {
    /**
     * appcode，比如剑侠世界，可能有绿色区、剑世区、怀旧区这几个选项，如没有则为空
     */
    navListCat: string;
  }) {
    this.report({
      key: 'ssgClickIntozone',
      desc: '点击进入游戏专区页',
      ext,
    });
  }
  /**
   * 点击文章列表页
   */
  ssgClickPagelist(ext: {
    /**
     * appcode，比如剑侠世界，可能有绿色区、剑世区、怀旧区这几个选项，如没有则为空
     */
    navListCat: string;
    /**
     * 跳转链接，如果没有则为空
     */
    title: string;
  }) {
    this.report({
      key: 'ssgClickPagelist',
      desc: '点击文章列表页',
      ext,
    });
  }

  /**
   * 点击专区页banner
   */
  ssgClickZonebanner(ext: {
    /**
     * appcode，比如剑侠世界，可能有绿色区、剑世区、怀旧区这几个选项，如没有则为空
     */
    navListCat: string;
    /**
     * 跳转链接，如果没有则为空
     */
    title: string;
  }) {
    this.report({
      key: 'ssgClickZonebanner',
      desc: '点击专区页banner',
      ext,
    });
  }

  /**
   * 点击专区新闻栏-banner
   */
  ssgClicknewsbanner(ext: {
    /**
     * appcode，比如剑侠世界，可能有绿色区、剑世区、怀旧区这几个选项，如没有则为空
     */
    navListCat: string;
    /**
     * 跳转链接，如果没有则为空
     */
    title: string;
  }) {
    this.report({
      key: 'ssgClicknewsbanner',
      desc: '点击专区新闻栏-banner',
      ext,
    });
  }

  /**
   * 点击专区页资讯栏
   */
  ssgClickZoneInforbar(ext: {
    /**
     * appcode，比如剑侠世界，可能有绿色区、剑世区、怀旧区这几个选项，如没有则为空
     */
    navListCat: string;
    /**
     * 自定义栏目：最新、新闻、活动、公告
     */
    type: string;
  }) {
    this.report({
      key: 'ssgClickZoneInforbar',
      desc: '点击专区页资讯栏',
      ext,
    });
  }

  /**
   * 点击置顶公告
   */
  ssgClickHeader(ext: {
    /**
     * appcode，比如剑侠世界，可能有绿色区、剑世区、怀旧区这几个选项，如没有则为空
     */
    navListCat: string;
    /**
     * 跳转链接，如果没有则为空
     */
    title: string;
  }) {
    this.report({
      key: 'ssgClickHeader',
      desc: '点击置顶公告',
      ext,
    });
  }

  /**
   * 点击专区页资讯内容
   */
  ssgClickZoneInforcomment(ext: {
    /**
     * appcode，比如剑侠世界，可能有绿色区、剑世区、怀旧区这几个选项，如没有则为空
     */
    navListCat: string;
    /**
     * 自定义栏目：最新、新闻、活动、公告
     */
    type: string;
    /**
     * 跳转链接，如果没有则为空
     */
    title: string;
  }) {
    this.report({
      key: 'ssgClickZoneInforcomment',
      desc: '点击专区页资讯内容',
      ext,
    });
  }

  /**
   * 点击快捷功能
   */
  ssgClickShortfunc(ext: {
    /**
     * appcode，比如剑侠世界，可能有绿色区、剑世区、怀旧区这几个选项，如没有则为空
     */
    navListCat: string;
    /**
     * 自定义功能，包含高亮及普通：更新日志、游戏官网、游戏充值、联系游戏
     */
    type: string;
    /**
     * 跳转链接，如果没有则为空
     */
    title: string;
  }) {
    this.report({
      key: 'ssgClickShortfunc',
      desc: '点击快捷功能',
      ext,
    });
  }

  /**
   * 操作游戏阵地
   */
  ssgGameCommunity(ext: {
    /**
     * appcode，比如剑侠世界，可能有绿色区、剑世区、怀旧区这几个选项，如没有则为空
     */
    navListCat: string;
    /**
     * 阵地的名称：如QQ、微信、微博
     */
    name: string;
    /**
     * 操作类型：显示二维码（display），点击（click）
     */
    action: 'display' | 'click';
  }) {
    this.report({
      key: 'ssgGameCommunity',
      desc: '操作游戏阵地',
      ext,
    });
  }

  /**
   * 点击扩展位类型
   */
  ssgClickExtendType(ext: {
    /**
     * appcode，比如剑侠世界，可能有绿色区、剑世区、怀旧区这几个选项，如没有则为空
     */
    navListCat: string;
    /**
     * 后台配置扩展位类型：宣传banner、战绩卡
     */
    type: string;
  }) {
    this.report({
      key: 'ssgClickExtendType',
      desc: '点击扩展位类型',
      ext,
    });
  }

  /**
   * 点击扩展位内容
   */
  ssgClickExtendContent(ext: {
    /**
     * appcode，比如剑侠世界，可能有绿色区、剑世区、怀旧区这几个选项，如没有则为空
     */
    navListCat: string;
    /**
     * 后台配置扩展位类型：宣传banner、战绩卡
     */
    type: string;
    /**
     * 扩展位内容枚举值：宣传banner、战绩卡、对局详情
     */
    content: string;
  }) {
    this.report({
      key: 'ssgClickExtendContent',
      desc: '点击扩展位内容',
      ext,
    });
  }

  /**
   * 点击战绩页tab
   */
  ssgClickMatchPage(ext: {
    /**
     * appcode，比如剑侠世界，可能有绿色区、剑世区、怀旧区这几个选项，如没有则为空
     */
    navListCat: string;
    /**
     * 分页标题枚举：赛季数据、对局记录
     */
    title: string;
  }) {
    this.report({
      key: 'ssgClickMatchPage',
      desc: '点击战绩页tab',
      ext,
    });
  }

  /**
   * 点击战绩分页游戏模式
   */
  ssgClickGameMode(ext: {
    /**
     * appcode，比如剑侠世界，可能有绿色区、剑世区、怀旧区这几个选项，如没有则为空
     */
    navListCat: string;
    /**
     * 游戏模式枚举：玛什马克、战场、王牌序列
     */
    mode: string;
  }) {
    this.report({
      key: 'ssgClickGameMode',
      desc: '点击战绩分页游戏模式',
      ext,
    });
  }

  /**
   * 点击排行榜tab
   */
  ssgClickRankPage(ext: {
    /**
     * appcode，比如剑侠世界，可能有绿色区、剑世区、怀旧区这几个选项，如没有则为空
     */
    navListCat: string;
    /**
     * tab标题枚举：男性造型、女性造型
     */
    title: string;
  }) {
    this.report({
      key: 'ssgClickRankPage',
      desc: '点击排行榜tab',
      ext,
    });
  }

  /**
   * 点击排行榜详情
   */
  ssgClickRankDetails(ext: {
    /**
     * appcode，比如剑侠世界，可能有绿色区、剑世区、怀旧区这几个选项，如没有则为空
     */
    navListCat: string;
    /**
     * tab标题枚举：男性造型、女性造型
     */
    title: string;
    /**
     * 数据条目的序号
     */
    Number: number;
  }) {
    this.report({
      key: 'ssgClickRankDetails',
      desc: '点击排行榜详情',
      ext,
    });
  }
}

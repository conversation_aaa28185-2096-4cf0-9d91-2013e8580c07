// vite.config.ts
import path from "node:path";
import { defineConfig, loadEnv, searchForWorkspaceRoot, splitVendorChunkPlugin } from "file:///C:/Users/<USER>/WebstormProjects/station-2.7/common/temp/node_modules/.pnpm/vite@5.0.7_@types+node@20.8.9/node_modules/vite/dist/node/index.js";
import UnoCSS from "file:///C:/Users/<USER>/WebstormProjects/station-2.7/common/temp/node_modules/.pnpm/unocss@0.58.0_postcss@8.4.32_vite@5.0.7/node_modules/unocss/dist/vite.mjs";
import preact from "file:///C:/Users/<USER>/WebstormProjects/station-2.7/common/temp/node_modules/.pnpm/@preact+preset-vite@2.7.0_@babel+core@7.23.5_preact@10.19.3_vite@5.0.7/node_modules/@preact/preset-vite/dist/esm/index.mjs";
import { rumVitePlugin } from "file:///C:/Users/<USER>/WebstormProjects/station-2.7/common/temp/node_modules/.pnpm/@arms+rum-vite-plugin@0.0.16/node_modules/@arms/rum-vite-plugin/dist/index.js";
import { combineConfig } from "file:///C:/Users/<USER>/WebstormProjects/station-2.7/libs/shared/src/vite.shared.config.js";
var __vite_injected_original_dirname = "C:\\Users\\<USER>\\WebstormProjects\\station-2.7\\apps\\main";
var vite_config_default = defineConfig(({ mode, command }) => {
  Object.assign(process.env, loadEnv(mode, path.join(__vite_injected_original_dirname, "env"), ""));
  const baseUrl = process.env.SSG_API_URL;
  return combineConfig({
    mode,
    base: "./",
    envDir: "./env",
    envPrefix: "SSG_",
    define: {
      "import.meta.vitest": "undefined"
    },
    optimizeDeps: {
      include: ["rxjs", "preact", "preact/devtools", "preact/debug", "preact/jsx-dev-runtime", "preact/hooks"],
      esbuildOptions: {
        tsconfigRaw: {
          compilerOptions: {
            experimentalDecorators: true
          }
        }
      }
    },
    build: {
      outDir: "dist/client",
      sourcemap: true
    },
    plugins: [
      UnoCSS(),
      preact({
        babel: {
          babelrc: false,
          plugins: [
            ["@babel/plugin-proposal-decorators", { legacy: true }],
            "babel-plugin-transform-typescript-metadata",
            ["@babel/plugin-transform-class-properties", { loose: true }]
          ]
        }
      }),
      splitVendorChunkPlugin(),
      rumVitePlugin({
        pid: process.env.SSG_RUM_PID,
        accessKeyId: process.env.SSG_RUM_AK,
        accessKeySecret: process.env.SSG_RUM_SK,
        region: process.env.SSG_RUM_REGION,
        version: process.env.SSG_RUM_VERSION
      })
    ],
    resolve: {
      alias: {
        "#app": path.resolve(__vite_injected_original_dirname, "app"),
        "#domains": path.resolve(__vite_injected_original_dirname, "domain"),
        "#common": path.resolve(__vite_injected_original_dirname, "common")
      }
    },
    server: {
      fs: {
        allow: [searchForWorkspaceRoot(process.cwd()), "../.."]
      },
      hmr: {
        port: 54320
      },
      proxy: {
        "^/api": {
          target: baseUrl,
          changeOrigin: true,
          rewrite: (path2) => path2.replace(/^\/api/, "")
        }
      }
    }
  });
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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

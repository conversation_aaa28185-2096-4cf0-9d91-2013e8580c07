import type { CSSProperties } from 'react';
import { useCallback, useEffect, useLayoutEffect, useRef, useState } from 'preact/hooks';
import { bus } from 'wujie';
import { BottomBarWithForwardRef } from '#app/bottomBar/view/BottomBar';
import { Launcher } from '#app/gameLauncher/view/Launcher';
import { MainViewer } from '#app/subappManager/view/WuJieViewer/WuJieViewer';
import MainViewerManager from '#app/subappManager/view/MainViewerManager';

export function Root() {
  const tick = useRef(false);
  const subAppRef = useRef<HTMLDivElement>(null);
  const barRef = useRef<HTMLDivElement>(null);
  const [subAppStyle, setSubAppStyle] = useState<CSSProperties>({});

  useLayoutEffect(() => {
    const barRect = barRef.current?.getBoundingClientRect();

    if (barRect) {
      setSubAppStyle({ height: (barRect?.height || 0) - 70 });
    }
  }, [barRef]);

  // 滚动事件
  const handleScroll = useCallback((ele: HTMLElement | Document) => {
    if ('documentElement' in ele) {
      bus.$emit('BACK_TOP', ele.documentElement.scrollTop > 200);
    } else {
      bus.$emit('BACK_TOP', ele.scrollTop > 200);
    }
    tick.current = false;
  }, []);

  useEffect(() => {
    const onScroll = (e: Event) => {
      if (!tick.current) {
        requestAnimationFrame(() => handleScroll(e.target as HTMLElement));
        tick.current = true;
      }
    };

    subAppRef.current.addEventListener('scroll', onScroll);

    return () => {
      subAppRef.current.removeEventListener('scroll', onScroll);
    };
  }, [handleScroll]);

  return (
    <div ref={subAppRef} className="h-full flex flex-col justify-between overflow-hidden">
      <div className="h-full">
        <MainViewerManager />
        <div style={subAppStyle} />
      </div>

      <BottomBarWithForwardRef ref={barRef} launcherSlot={<Launcher />} />
    </div>
  );
}

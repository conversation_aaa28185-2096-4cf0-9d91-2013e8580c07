/**
 * IFrame对话框相关类型定义
 */

/**
 * IFrame加载状态枚举
 */
export enum IFrameLoadState {
  /** 空闲状态 */
  IDLE = 'idle',
  /** 加载中 */
  LOADING = 'loading',
  /** 加载成功 */
  SUCCESS = 'success',
  /** 加载失败 */
  ERROR = 'error',
  /** 加载超时 */
  TIMEOUT = 'timeout'
}

/**
 * IFrame错误类型
 */
export interface IFrameError {
  /** 错误类型 */
  type: 'load_error' | 'timeout' | 'network_error' | 'unknown';
  /** 错误消息 */
  message: string;
  /** 错误发生的URL */
  url?: string;
  /** 错误发生时间 */
  timestamp: number;
}

/**
 * IFrame加载事件数据
 */
export interface IFrameLoadEvent {
  /** 加载的URL */
  url: string;
  /** 加载开始时间 */
  startTime: number;
  /** 加载结束时间 */
  endTime: number;
  /** 加载耗时（毫秒） */
  duration: number;
}

/**
 * IFrame对话框配置选项
 */
export interface IFrameDialogConfig {
  /** 默认宽度 */
  defaultWidth?: string;
  /** 默认高度 */
  defaultHeight?: string;
  /** 默认超时时间 */
  defaultTimeout?: number;
  /** 是否显示加载动画 */
  showLoading?: boolean;
  /** 是否显示错误重试 */
  showRetry?: boolean;
  /** 是否允许点击遮罩关闭 */
  closeOnOverlayClick?: boolean;
}

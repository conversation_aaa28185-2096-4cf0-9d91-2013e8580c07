import { useState } from 'preact/hooks';
import { IFrameDialog } from './IFrameDialog';

/**
 * IFrameDialog 使用示例组件
 * 展示如何使用 IFrameDialog 组件的各种功能
 */
export function IFrameDialogExample() {
  // 对话框状态管理（用于组件方式）
  const [isOpen, setIsOpen] = useState(false);
  const [currentUrl, setCurrentUrl] = useState('');

  /**
   * 打开对话框（组件方式）
   * @param url - 要加载的URL
   */
  const openDialog = (url: string) => {
    setCurrentUrl(url);
    setIsOpen(true);
  };

  /**
   * 关闭对话框（组件方式）
   */
  const closeDialog = () => {
    setIsOpen(false);
    setCurrentUrl('');
  };

  /**
   * 使用静态方法打开对话框
   * @param url - 要加载的URL
   * @param title - 对话框标题
   */
  const openStaticDialog = (url: string, title: string) => {
    const dialog = IFrameDialog.openDialog({
      title,
      src: url,
      width: '900px',
      height: '700px',
      timeout: 10000, // 10秒超时用于演示
      onLoad: () => {
        console.log('静态对话框加载成功:', url);
      },
      onError: (error) => {
        console.error('静态对话框加载失败:', error.message);
      },
      onClose: () => {
        console.log('静态对话框已关闭');
      }
    });

    // 示例：5秒后自动关闭（仅用于演示）
    if (url.includes('auto-close')) {
      setTimeout(() => {
        dialog.close();
      }, 5000);
    }
  };

  /**
   * 加载成功回调
   */
  const handleLoad = () => {
    console.log('IFrame加载成功');
  };

  /**
   * 加载失败回调
   */
  const handleError = (error: Error) => {
    console.error('IFrame加载失败:', error.message);
  };

  return (
    <div className="p-8 space-y-6">
      <h2 className="text-xl font-bold text-white mb-6">IFrameDialog 示例</h2>

      {/* 组件方式示例 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-white">组件方式使用</h3>
        <div className="space-y-2">
          <button
            className="block w-full max-w-xs px-4 py-2 text-left text-white bg-blue-600 hover:bg-blue-700 rounded"
            onClick={() => openDialog('https://www.baidu.com')}
          >
            打开百度（正常加载）
          </button>

          <button
            className="block w-full max-w-xs px-4 py-2 text-left text-white bg-green-600 hover:bg-green-700 rounded"
            onClick={() => openDialog('https://github.com')}
          >
            打开GitHub（正常加载）
          </button>

          <button
            className="block w-full max-w-xs px-4 py-2 text-left text-white bg-red-600 hover:bg-red-700 rounded"
            onClick={() => openDialog('https://invalid-url-that-will-fail.com')}
          >
            打开无效URL（加载失败）
          </button>

          <button
            className="block w-full max-w-xs px-4 py-2 text-left text-white bg-yellow-600 hover:bg-yellow-700 rounded"
            onClick={() => openDialog('https://httpstat.us/200?sleep=35000')}
          >
            打开慢速URL（超时测试）
          </button>
        </div>
      </div>

      {/* 静态方法示例 */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-white">静态方法使用 (openDialog)</h3>
        <div className="space-y-2">
          <button
            className="block w-full max-w-xs px-4 py-2 text-left text-white bg-purple-600 hover:bg-purple-700 rounded"
            onClick={() => openStaticDialog('https://www.baidu.com', '静态百度对话框')}
          >
            静态打开百度
          </button>

          <button
            className="block w-full max-w-xs px-4 py-2 text-left text-white bg-indigo-600 hover:bg-indigo-700 rounded"
            onClick={() => openStaticDialog('https://github.com', '静态GitHub对话框')}
          >
            静态打开GitHub
          </button>

          <button
            className="block w-full max-w-xs px-4 py-2 text-left text-white bg-pink-600 hover:bg-pink-700 rounded"
            onClick={() => openStaticDialog('https://www.baidu.com?auto-close=true', '自动关闭对话框')}
          >
            静态打开（5秒后自动关闭）
          </button>

          <button
            className="block w-full max-w-xs px-4 py-2 text-left text-white bg-orange-600 hover:bg-orange-700 rounded"
            onClick={() => openStaticDialog('https://invalid-url-static.com', '静态错误对话框')}
          >
            静态打开无效URL
          </button>
        </div>
      </div>

      {/* 组件方式的 IFrameDialog */}
      <IFrameDialog
        title="IFrame 对话框"
        open={isOpen}
        src={currentUrl}
        width="900px"
        height="700px"
        timeout={10000} // 10秒超时用于演示
        onClose={closeDialog}
        onLoad={handleLoad}
        onError={handleError}
      />
    </div>
  );
}

import { useState } from 'preact/hooks';
import { IFrameDialog } from './IFrameDialog';

/**
 * IFrameDialog 使用示例组件
 * 展示如何使用 IFrameDialog 组件的各种功能
 */
export function IFrameDialogExample() {
  // 对话框状态管理
  const [isOpen, setIsOpen] = useState(false);
  const [currentUrl, setCurrentUrl] = useState('');

  /**
   * 打开对话框
   * @param url - 要加载的URL
   */
  const openDialog = (url: string) => {
    setCurrentUrl(url);
    setIsOpen(true);
  };

  /**
   * 关闭对话框
   */
  const closeDialog = () => {
    setIsOpen(false);
    setCurrentUrl('');
  };

  /**
   * 加载成功回调
   */
  const handleLoad = () => {
    console.log('IFrame加载成功');
  };

  /**
   * 加载失败回调
   */
  const handleError = (error: Error) => {
    console.error('IFrame加载失败:', error.message);
  };

  return (
    <div className="p-8 space-y-4">
      <h2 className="text-xl font-bold text-white mb-6">IFrameDialog 示例</h2>
      
      {/* 示例按钮 */}
      <div className="space-y-2">
        <button
          className="block w-full max-w-xs px-4 py-2 text-left text-white bg-blue-600 hover:bg-blue-700 rounded"
          onClick={() => openDialog('https://www.baidu.com')}
        >
          打开百度（正常加载）
        </button>
        
        <button
          className="block w-full max-w-xs px-4 py-2 text-left text-white bg-green-600 hover:bg-green-700 rounded"
          onClick={() => openDialog('https://github.com')}
        >
          打开GitHub（正常加载）
        </button>
        
        <button
          className="block w-full max-w-xs px-4 py-2 text-left text-white bg-red-600 hover:bg-red-700 rounded"
          onClick={() => openDialog('https://invalid-url-that-will-fail.com')}
        >
          打开无效URL（加载失败）
        </button>
        
        <button
          className="block w-full max-w-xs px-4 py-2 text-left text-white bg-yellow-600 hover:bg-yellow-700 rounded"
          onClick={() => openDialog('https://httpstat.us/200?sleep=35000')}
        >
          打开慢速URL（超时测试）
        </button>
      </div>

      {/* IFrameDialog 组件 */}
      <IFrameDialog
        title="IFrame 对话框"
        open={isOpen}
        src={currentUrl}
        width="900px"
        height="700px"
        timeout={10000} // 10秒超时用于演示
        onClose={closeDialog}
        onLoad={handleLoad}
        onError={handleError}
      />
    </div>
  );
}

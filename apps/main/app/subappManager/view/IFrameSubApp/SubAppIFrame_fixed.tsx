import type { HTMLAttributes } from 'preact/compat';
import type { startOptions } from 'wujie';
import { useRef, useState, useEffect, useCallback } from 'preact/hooks';
import { AboutLauncherTracking } from '@ssglauncher/tracking';
import { ConnectFailed } from '../../component/ConnectFailed';
import { logger } from '#common/util/logger';
import loading from '../../assets/Loading.webp';

// 生命周期回调类型 / Lifecycle callback type
type TLifeCycle = startOptions['activated'];

interface SubAppIFrameProps extends HTMLAttributes<HTMLDivElement> {
  app: Omit<startOptions, 'el'>;
  reload?: () => void;
  onActivated?: TLifeCycle;
}

const LOAD_TIMEOUT = 30000; // 30秒超时 / 30 second timeout

const SubAppIFrame = (props: SubAppIFrameProps) => {
  const { app, onActivated, className, style, ...divProps } = props;

  const iframeRef = useRef<HTMLIFrameElement>(null);
  const loadTimeoutRef = useRef<NodeJS.Timeout>();
  const isMountedRef = useRef(true);

  const [isError, setIsError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // 清除超时定时器 / Clear timeout timer
  const clearLoadTimeout = useCallback(() => {
    if (loadTimeoutRef.current) {
      clearTimeout(loadTimeoutRef.current);
      loadTimeoutRef.current = undefined;
    }
  }, []);

  // 设置错误状态 / Set error state
  const setErrorState = useCallback((error: Error) => {
    if (!isMountedRef.current) return;

    console.error('IFrame加载失败 / IFrame load failed:', error.message, 'URL:', app.url);
    logger('[SubAppIFrame] Error', { url: app.url, error: error.message });

    clearLoadTimeout();
    setIsError(true);
    setIsLoading(false);
  }, [app.url, clearLoadTimeout]);

  // iframe加载成功处理 / Handle iframe load success
  const handleIFrameLoad = useCallback(() => {
    if (!isMountedRef.current) return;

    clearLoadTimeout();
    setIsLoading(false);
    setIsError(false);

    logger('[SubAppIFrame] Load success', { url: app.url });
    onActivated?.(null);
  }, [app.url, clearLoadTimeout, onActivated]);

  // iframe加载失败处理 / Handle iframe load error
  const handleIFrameError = useCallback(() => {
    setErrorState(new Error('IFrame load failed'));
  }, [setErrorState]);

  // 重新加载 / Reload
  const reload = useCallback(() => {
    if (!isMountedRef.current) return;

    setIsError(false);
    setIsLoading(true);
    clearLoadTimeout();

    // 重新设置iframe src触发重新加载 / Reset iframe src to trigger reload
    if (iframeRef.current) {
      iframeRef.current.src = app.url;
    }

    // 上报重载事件 / Report reload event
    new AboutLauncherTracking((p: any) => (window as any).xcef?.dataAnalysisModule?.report(p))
      .ssgReloadEvent({ reloadSource: app.url });
  }, [app.url, clearLoadTimeout]);

  // URL变化时重新加载 / Reload when URL changes
  useEffect(() => {
    if (!isMountedRef.current) return;

    setIsError(false);
    setIsLoading(true);
    clearLoadTimeout();

    // 设置加载超时 / Set load timeout
    loadTimeoutRef.current = setTimeout(() => {
      if (isMountedRef.current && isLoading) {
        setErrorState(new Error('IFrame load timeout'));
      }
    }, LOAD_TIMEOUT);

    logger('[SubAppIFrame] Loading', { url: app.url });
  }, [app.url, clearLoadTimeout, setErrorState, isLoading]);

  // 组件挂载时调用激活回调 / Call activation callback on mount
  useEffect(() => {
    onActivated?.(null);
  }, [onActivated]);

  // 组件卸载清理 / Cleanup on unmount
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      clearLoadTimeout();

      logger('[SubAppIFrame] Destroy', { url: app.url });
    };
  }, [app.url, clearLoadTimeout]);

  // 渲染加载状态 / Render loading state
  const renderLoading = () => {
    if (!isLoading) return null;

    return (
      <div className="absolute inset-0 flex items-center justify-center z-10">
        <div className="flex items-center justify-center -mt-0px">
          <img src={loading} alt="Loading..." />
        </div>
      </div>
    );
  };

  // 渲染内容 / Render content
  if (isError) {
    return (
      <div className="h-full" {...divProps}>
        <ConnectFailed onClick={reload} />
      </div>
    );
  }

  return (
    <div className="h-full" {...divProps}>
      <div className={`${className || ''} relative h-full`} style={style}>
        {renderLoading()}
        <iframe
          ref={iframeRef}
          src={app.url}
          className="w-full h-full border-0"
          onLoad={handleIFrameLoad}
          onError={handleIFrameError}
          sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-modals allow-top-navigation"
          title={app.name}
        />
      </div>
    </div>
  );
};

// Static methods to maintain compatibility with SubApp interface
const SubAppIFrameWithStatics = Object.assign(SubAppIFrame, {
  bus: {
    $on: () => {},
    $off: () => {},
    $emit: () => {},
  },
  setupApp: () => Promise.resolve(),
  preloadApp: () => Promise.resolve(),
  destroyApp: () => Promise.resolve(),
});

export default SubAppIFrameWithStatics;

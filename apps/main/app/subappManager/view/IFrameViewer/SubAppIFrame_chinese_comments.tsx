import type { HTMLAttributes } from 'preact/compat';
import type { startOptions } from 'wujie';
import { useRef, useState, useEffect, useCallback, useMemo } from 'preact/hooks';
import { AboutLauncherTracking } from '@ssglauncher/tracking';
import { ConnectFailed } from '../../component/ConnectFailed';
import { logger } from '#common/util/logger';
import { SubAppLoading } from '#app/subappManager/component/SubAppLoading';

/**
 * 生命周期回调类型，用于处理子应用的激活/停用事件
 * 所有生命周期回调的实现都是一样的，可以任意选择一个
 */
type TLifeCycle = startOptions['activated'];

/**
 * SubAppIFrame 组件属性接口
 * 继承自 HTMLDivElement 的所有标准属性，同时添加子应用特定的配置
 */
interface SubAppIFrameProps extends HTMLAttributes<HTMLDivElement> {
  /** 子应用配置对象，包含URL、名称、属性等信息（排除el元素） */
  app: Omit<startOptions, 'el'>;
  
  /** 可选的重载函数，用于刷新子应用 */
  reload?: () => void;
  
  /** 可选的激活回调，当子应用成功加载时触发 */
  onActivated?: TLifeCycle;
}

/**
 * IFrame加载超时时间（毫秒）
 * 设置为30秒，平衡用户体验和网络条件
 */
const LOAD_TIMEOUT = 30000;

/**
 * SubAppIFrame 组件 - 用于IFrame中显示子应用
 *
 * 功能特性:
 * - 子应用IFrame容器
 * - 加载状态管理
 * - 错误处理和重试
 * - 超时检测
 * - 生命周期管理
 * - 与 SubApp 接口兼容
 *
 * 静态方法用于保持与 SubApp 接口的兼容性
 */
const SubAppIFrame = (props: SubAppIFrameProps) => {
  // 解构组件属性
  const { app, onActivated, className, style, ...divProps } = props;

  // React Refs 用于 DOM 元素引用和状态管理
  
  /** IFrame元素引用 */
  const iframeRef = useRef<HTMLIFrameElement>(null);
  
  /** 加载超时定时器引用 */
  const loadTimeoutRef = useRef<NodeJS.Timeout>();
  
  /** 组件挂载状态引用 */
  const isMountedRef = useRef(true);

  // 组件状态管理
  
  /** 是否出现错误状态 */
  const [isError, setIsError] = useState(false);
  
  /** 是否正在加载状态 */
  const [isLoading, setIsLoading] = useState(true);

  // 生成带props的URL
  const iframeSrc = useMemo(() => {
    const { props: appProps, url } = app;
    if (!appProps) return url;
    
    const uri = new URL(url);
    uri.searchParams.set('$props', JSON.stringify(appProps));
    return uri.toString();
  }, [app.url, app.props]);

  // 清除超时定时器
  const clearLoadTimeout = useCallback(() => {
    if (loadTimeoutRef.current) {
      clearTimeout(loadTimeoutRef.current);
      loadTimeoutRef.current = undefined;
    }
  }, []);

  // 设置错误状态
  const setErrorState = useCallback((error: Error) => {
    if (!isMountedRef.current) return;
    
    console.error('IFrame加载失败:', error.message, 'URL:', app.url);
    logger('[SubAppIFrame] Error', { url: app.url, error: error.message });
    
    clearLoadTimeout();
    setIsError(true);
    setIsLoading(false);
  }, [app.url, clearLoadTimeout]);

  /**
   * IFrame加载成功处理函数
   * 当IFrame成功加载时调用，清除超时定时器并更新状态
   * 重要：必须清除超时定时器以防止竞态条件
   */
  const handleIFrameLoad = useCallback(() => {
    if (!isMountedRef.current) return;
    
    // 清除超时定时器，防止加载成功后仍触发超时错误
    clearLoadTimeout();
    
    // 更新组件状态
    setIsLoading(false);
    setIsError(false);
    
    logger('[SubAppIFrame] Load success', { url: app.url });
    
    // 调用生命周期回调
    onActivated?.(null);
  }, [app.url, clearLoadTimeout, onActivated]);

  /**
   * IFrame加载错误处理函数
   * 当IFrame加载出错时调用，触发错误处理流程
   */
  const handleIFrameError = useCallback(() => {
    setErrorState(new Error('IFrame load failed'));
  }, [setErrorState]);

  /**
   * 重新加载函数
   * 重置状态并重新加载IFrame
   */
  const reload = useCallback(() => {
    if (!isMountedRef.current) return;
    
    setIsError(false);
    setIsLoading(true);
    clearLoadTimeout();
    
    // 重新设置iframe src触发重新加载
    if (iframeRef.current) {
      iframeRef.current.src = iframeSrc;
    }
    
    // 上报重载事件
    new AboutLauncherTracking((p: any) => (window as any).xcef?.dataAnalysisModule?.report(p))
      .ssgReloadEvent({ reloadSource: app.url });
  }, [app.url, iframeSrc, clearLoadTimeout]);

  // URL变化时重新加载
  useEffect(() => {
    if (!isMountedRef.current) return;
    
    setIsError(false);
    setIsLoading(true);
    clearLoadTimeout();
    
    // 设置加载超时
    loadTimeoutRef.current = setTimeout(() => {
      if (isMountedRef.current && isLoading) {
        setErrorState(new Error('IFrame load timeout'));
      }
    }, LOAD_TIMEOUT);
    
    logger('[SubAppIFrame] Loading', { url: app.url });
  }, [app.url, clearLoadTimeout, setErrorState, isLoading]);

  // 组件挂载时调用激活回调
  useEffect(() => {
    onActivated?.(null);
  }, [onActivated]);

  // 组件卸载清理
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      clearLoadTimeout();
    };
  }, [clearLoadTimeout]);

  // 渲染内容
  if (isError) {
    return (
      <div className="h-full" {...divProps}>
        <ConnectFailed onClick={reload} />
      </div>
    );
  }

  return (
    <div className="h-full" {...divProps}>
      <div className={`${className || ''} relative h-full`} style={style}>
        {isLoading && <SubAppLoading />}
        <iframe
          ref={iframeRef}
          src={iframeSrc}
          className="w-full h-full border-0"
          onLoad={handleIFrameLoad}
          onError={handleIFrameError}
          sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-modals allow-top-navigation"
          title={app.name}
        />
      </div>
    </div>
  );
};

// 静态方法用于保持与 SubApp 接口的兼容性
const SubAppIFrameWithStatics = Object.assign(SubAppIFrame, {
  bus: {
    $on: () => {},
    $off: () => {},
    $emit: () => {},
  },
  setupApp: () => Promise.resolve(),
  preloadApp: () => Promise.resolve(),
  destroyApp: () => Promise.resolve(),
});

export default SubAppIFrameWithStatics;

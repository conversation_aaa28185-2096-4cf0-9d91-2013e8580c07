import type { HTMLAttributes } from 'preact/compat';
import type { startOptions } from 'wujie';
import { useRef, useState, useEffect, useCallback } from 'preact/hooks';
import { AboutLauncherTracking } from '@ssglauncher/tracking';
import { ConnectFailed } from '../../component/ConnectFailed';
import { logger } from '#common/util/logger';
import loading from '../../assets/Loading.webp';
import { SubAppLoading } from '#app/subappManager/component/SubAppLoading';

/**
 * 加载图片资源配置
 * Loading image resources configuration
 */
const images = {
  loading,
};

/**
 * 生命周期回调类型，用于处理子应用的激活/停用事件
 * Lifecycle callback type for handling sub-app activation/deactivation events
 *
 * 所有生命周期回调的实现都是一样的，可以任意选择一个
 * All lifecycle callbacks have the same implementation, can choose any one
 */
type TLifeCycle = startOptions['activated'];

/**
 * SubAppIFrame 组件属性接口
 * SubAppIFrame component props interface
 *
 * 继承自 HTMLDivElement 的所有标准属性，同时添加子应用特定的配置
 * Extends all standard HTMLDivElement attributes while adding sub-app specific configurations
 */
interface SubAppIFrameProps extends HTMLAttributes<HTMLDivElement> {
  /** 子应用配置对象，包含URL、名称、属性等信息（排除el元素） */
  /** Sub-app configuration object containing URL, name, props, etc. (excluding el element) */
  app: Omit<startOptions, 'el'>;

  /** 可选的重载函数，用于刷新子应用 */
  /** Optional reload function for refreshing the sub-app */
  reload?: () => void;

  /** 可选的激活回调，当子应用成功加载时触发 */
  /** Optional activation callback triggered when sub-app loads successfully */
  onActivated?: TLifeCycle;
}

// Static methods to maintain compatibility with SubApp interface
const SubAppIFrame = Object.assign(
  (props: SubAppIFrameProps) => {
    const { app, onActivated, className, style, ...divProps } = props;

    const containerRef = useRef<HTMLDivElement>(null);
    const iframeRef = useRef<HTMLIFrameElement>(null);
    const loadTimeoutRef = useRef<NodeJS.Timeout>();

    const [isError, setIsError] = useState(false);
    const [isLoading, setIsLoading] = useState(true);

    const loadError = useCallback((url: string, e: Error) => {
      console.error('页面连接失败，错误：%s，连接地址：%s', e, url);
      setIsError(true);
      setIsLoading(false);
    }, []);

    const handleIFrameLoad = useCallback(() => {
      clearTimeout(loadTimeoutRef.current);

      setIsLoading(false);
      setIsError(false);

      // Call lifecycle callbacks
      if (onActivated) {
        onActivated(null);
      }
    }, [onActivated]);

    const handleIFrameError = useCallback(() => {
      loadError(app.url, new Error('IFrame load failed'));
    }, [app.url, loadError]);

    const reload = useCallback(() => {
      if (iframeRef.current) {
        setIsError(false);
        setIsLoading(true);
        iframeRef.current.src = app.url;
      } else {
        window.location.reload();
      }

      new AboutLauncherTracking((p: any) => (window as any).xcef.dataAnalysisModule.report(p)).ssgReloadEvent({
        reloadSource: app.url,
      });
    }, [app.url]);

    const start = async () => {
      logger('[SubAppIFrame]', { url: app.url });

      if (!isLoading) {
        return;
      }

      try {
        // Set a timeout for iframe loading
        loadTimeoutRef.current = setTimeout(() => {
          if (isLoading) {
            loadError(app.url, new Error('IFrame load timeout'));
          }
        }, 30000); // 30 second timeout

        // The iframe will handle the actual loading
        // State will be updated via handleIFrameLoad or handleIFrameError
      } catch (error) {
        console.error('iframe fetch 异常：%s', error);
        setIsError(true);
        setIsLoading(false);
      }
    };

    // Update effect
    useEffect(() => {
      setIsError(false);
      setIsLoading(true);
      start();
    }, [app.url]);

    useEffect(() => {
      return () => {
        if (loadTimeoutRef.current) {
          clearTimeout(loadTimeoutRef.current);
        }
      };
    }, []);

    const renderContent = () => {
      if (isError) {
        return <ConnectFailed onClick={reload} />;
      }

      const getSrc = () => {
        const { props, url } = app;
        const uri = new URL(url);
        uri.searchParams.set('$props', JSON.stringify(props));
        return uri.toString();
      };

      return (
        <div className={`${className || ''} relative h-full`} style={style}>
          {isLoading && <SubAppLoading />}
          {/* eslint-disable-next-line react/iframe-missing-sandbox */}
          <iframe
            ref={iframeRef}
            src={getSrc()}
            className="w-full h-full border-0"
            onLoad={handleIFrameLoad}
            onError={handleIFrameError}
            sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-modals allow-top-navigation"
            title={app.name}
          />
        </div>
      );
    };

    useEffect(() => {
      onActivated(null);
    }, []);

    return (
      <div ref={containerRef} className="h-full" {...divProps}>
        {renderContent()}
      </div>
    );
  },
  {
    // Static methods to maintain compatibility with SubApp interface
    bus: {
      $on: () => {},
      $off: () => {},
      $emit: () => {},
    },
    setupApp: () => Promise.resolve(),
    preloadApp: () => Promise.resolve(),
    destroyApp: () => Promise.resolve(),
  },
);

export default SubAppIFrame;

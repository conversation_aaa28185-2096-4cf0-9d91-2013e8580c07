import type { HTMLAttributes } from 'preact/compat';
import type { startOptions } from 'wujie';
import { useRef, useState, useEffect, useCallback } from 'preact/hooks';
import { AboutLauncherTracking } from '@ssglauncher/tracking';
import { ConnectFailed } from '../../component/ConnectFailed';
import { logger } from '#common/util/logger';
import { SubAppLoading } from '#app/subappManager/component/SubAppLoading';
import loading from '../../assets/Loading.webp';

/**
 * 生命周期回调类型，用于处理子应用的激活/停用事件
 * 所有生命周期回调的实现都是一样的，可以任意选择一个
 */
type TLifeCycle = startOptions['activated'];

/**
 * SubAppIFrame 组件属性接口
 * 继承自 HTMLDivElement 的所有标准属性，同时添加子应用特定的配置
 */
interface SubAppIFrameProps extends HTMLAttributes<HTMLDivElement> {
  /** 子应用配置对象，包含URL、名称、属性等信息（排除el元素） */
  app: Omit<startOptions, 'el'>;

  /** 可选的重载函数，用于刷新子应用 */
  reload?: () => void;

  /** 可选的激活回调，当子应用成功加载时触发 */
  onActivated?: TLifeCycle;
}

/**
 * SubAppIFrame 组件 - 用于IFrame中显示子应用
 *
 * 功能特性:
 * - 子应用IFrame容器
 * - 加载状态管理
 * - 错误处理和重试
 * - 超时检测
 * - 生命周期管理
 * - 与 SubApp 接口兼容
 *
 * 静态方法用于保持与 SubApp 接口的兼容性
 */
const SubAppIFrame = Object.assign(
  (props: SubAppIFrameProps) => {
    // 解构组件属性
    const { app, onActivated, className, style, ...divProps } = props;

    // React Refs 用于 DOM 元素引用和状态管理

    /** 容器元素引用 */
    const containerRef = useRef<HTMLDivElement>(null);

    /** IFrame元素引用 */
    const iframeRef = useRef<HTMLIFrameElement>(null);

    /** 加载超时定时器引用 */
    const loadTimeoutRef = useRef<NodeJS.Timeout>();

    // 组件状态管理

    /** 是否出现错误状态 */
    const [isError, setIsError] = useState(false);

    /** 是否正在加载状态 */
    const [isLoading, setIsLoading] = useState(true);

    /**
     * 加载错误处理函数
     * 当IFrame加载失败时调用，设置错误状态并停止加载
     *
     * @param url - 失败的URL地址
     * @param e - 错误对象
     */
    const loadError = useCallback((url: string, e: Error) => {
      console.error('页面连接失败，错误：%s，连接地址：%s', e, url);
      setIsError(true);
      setIsLoading(false);
    }, []);

    /**
     * IFrame加载成功处理函数
     * 当IFrame成功加载时调用，清除超时定时器并更新状态
     * 重要：必须清除超时定时器以防止竞态条件
     */
    const handleIFrameLoad = useCallback(() => {
      // 清除超时定时器，防止加载成功后仍触发超时错误
      clearTimeout(loadTimeoutRef.current);

      // 更新组件状态
      setIsLoading(false);
      setIsError(false);

      // 调用生命周期回调
      if (onActivated) {
        onActivated(null);
      }
    }, [onActivated]);

    /**
     * IFrame加载错误处理函数
     * 当IFrame加载出错时调用，触发错误处理流程
     */
    const handleIFrameError = useCallback(() => {
      loadError(app.url, new Error('IFrame load failed'));
    }, [app.url, loadError]);

    /**
     * 重新加载函数
     * 重置错误状态并重新加载iframe
     */
    const reload = useCallback(() => {
      if (iframeRef.current) {
        setIsError(false);
        setIsLoading(true);
        iframeRef.current.src = app.url;
      } else {
        window.location.reload();
      }

      // 上报重载事件
      new AboutLauncherTracking((p: any) => (window as any).xcef.dataAnalysisModule.report(p)).ssgReloadEvent({
        reloadSource: app.url,
      });
    }, [app.url]);

    /**
     * 启动加载函数
     * 初始化iframe加载过程并设置超时
     */
    const start = async () => {
      logger('[SubAppIFrame]', { url: app.url });

      if (!isLoading) {
        return;
      }

      try {
        // 设置 iframe 加载超时
        loadTimeoutRef.current = setTimeout(() => {
          if (isLoading) {
            loadError(app.url, new Error('IFrame load timeout'));
          }
        }, 30000); // 30秒超时

        // iframe 将处理实际加载
        // 状态将通过 handleIFrameLoad 或 handleIFrameError 更新
      } catch (error) {
        console.error('iframe fetch 异常：%s', error);
        setIsError(true);
        setIsLoading(false);
      }
    };

    // URL更新时重新加载
    useEffect(() => {
      setIsError(false);
      setIsLoading(true);
      start();
    }, [app.url]);

    // 组件卸载时清理资源
    useEffect(() => {
      return () => {
        if (loadTimeoutRef.current) {
          clearTimeout(loadTimeoutRef.current);
        }
      };
    }, []);

    /**
     * 渲染内容函数
     * 根据状态渲染错误页面或iframe
     */
    const renderContent = () => {
      if (isError) {
        return <ConnectFailed onClick={reload} />;
      }

      // 生成带props参数的URL
      const getSrc = () => {
        const { props, url } = app;
        const uri = new URL(url);
        uri.searchParams.set('$props', JSON.stringify(props));
        return uri.toString();
      };

      return (
        <div className={`${className || ''} relative h-full`} style={style}>
          {isLoading && <SubAppLoading />}
          <iframe
            ref={iframeRef}
            src={getSrc()}
            className="w-full h-full border-0"
            onLoad={handleIFrameLoad}
            onError={handleIFrameError}
            sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-modals allow-top-navigation"
            title={app.name}
          />
        </div>
      );
    };

    // 组件初始化时调用激活回调
    useEffect(() => {
      onActivated(null);
    }, []);

    return (
      <div ref={containerRef} className="h-full" {...divProps}>
        {renderContent()}
      </div>
    );
  },
  {
    // 静态方法用于保持与 SubApp 接口的兼容性
    bus: {
      $on: () => {},
      $off: () => {},
      $emit: () => {},
    },
    setupApp: () => Promise.resolve(),
    preloadApp: () => Promise.resolve(),
    destroyApp: () => Promise.resolve(),
  },
);

export default SubAppIFrame;

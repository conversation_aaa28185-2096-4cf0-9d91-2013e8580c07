import type { HTMLAttributes } from 'preact/compat';
import type { startOptions } from 'wujie';
import { useRef, useState, useEffect, useCallback } from 'preact/hooks';
import { AboutLauncherTracking } from '@ssglauncher/tracking';
import { ConnectFailed } from '../../component/ConnectFailed';
import { logger } from '#common/util/logger';
import loading from '../../assets/Loading.webp';
import { SubAppLoading } from '#app/subappManager/component/SubAppLoading';

const images = {
  loading,
};

// 其背后实现都是 lifecycle，随便选一个都一样
type TLifeCycle = startOptions['activated'];

interface SubAppIFrameProps extends HTMLAttributes<HTMLDivElement> {
  app: Omit<startOptions, 'el'>;
  reload?: () => void;
  onActivated?: TLifeCycle;
}

// Static methods to maintain compatibility with SubApp interface
const SubAppIFrame = Object.assign(
  (props: SubAppIFrameProps) => {
    const { app, onActivated, className, style, ...divProps } = props;

    const containerRef = useRef<HTMLDivElement>(null);
    const iframeRef = useRef<HTMLIFrameElement>(null);
    const loadTimeoutRef = useRef<NodeJS.Timeout>();

    const [isError, setIsError] = useState(false);
    const [isLoading, setIsLoading] = useState(true);

    const loadError = useCallback((url: string, e: Error) => {
      console.error('页面连接失败，错误：%s，连接地址：%s', e, url);
      setIsError(true);
      setIsLoading(false);
    }, []);

    const handleIFrameLoad = useCallback(() => {
      clearTimeout(loadTimeoutRef.current);

      setIsLoading(false);
      setIsError(false);

      // Call lifecycle callbacks
      if (onActivated) {
        onActivated(null);
      }
    }, [onActivated]);

    const handleIFrameError = useCallback(() => {
      loadError(app.url, new Error('IFrame load failed'));
    }, [app.url, loadError]);

    const reload = useCallback(() => {
      if (iframeRef.current) {
        setIsError(false);
        setIsLoading(true);
        iframeRef.current.src = app.url;
      } else {
        window.location.reload();
      }

      new AboutLauncherTracking((p: any) => (window as any).xcef.dataAnalysisModule.report(p)).ssgReloadEvent({
        reloadSource: app.url,
      });
    }, [app.url]);

    const start = useCallback(async () => {
      logger('[SubAppIFrame]', { url: app.url });

      if (!isLoading) {
        return;
      }

      try {
        // Set a timeout for iframe loading
        loadTimeoutRef.current = setTimeout(() => {
          if (isLoading) {
            loadError(app.url, new Error('IFrame load timeout'));
          }
        }, 30000); // 30 second timeout

        // The iframe will handle the actual loading
        // State will be updated via handleIFrameLoad or handleIFrameError
      } catch (error) {
        console.error('iframe fetch 异常：%s', error);
        setIsError(true);
        setIsLoading(false);
      }
    }, [app.url, isLoading, loadError]);

    // Update effect
    useEffect(() => {
      setIsError(false);
      setIsLoading(true);
    }, [app.url]);

    useEffect(() => {
      return () => {
        if (loadTimeoutRef.current) {
          clearTimeout(loadTimeoutRef.current);
        }
      };
    }, []);

    const renderContent = () => {
      if (isError) {
        return <ConnectFailed onClick={reload} />;
      }

      const getSrc = () => {
        const { props, url } = app;
        const uri = new URL(url);
        uri.searchParams.set('$props', JSON.stringify(props));
        return uri.toString();
      };

      return (
        <div className={`${className || ''} relative h-full`} style={style}>
          {isLoading && <SubAppLoading />}
          {/* eslint-disable-next-line react/iframe-missing-sandbox */}
          <iframe
            ref={iframeRef}
            src={getSrc()}
            className="w-full h-full border-0"
            onLoad={handleIFrameLoad}
            onError={handleIFrameError}
            sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-modals allow-top-navigation"
            title={app.name}
          />
        </div>
      );
    };

    useEffect(() => {
      onActivated(null);
    }, []);

    return (
      <div ref={containerRef} className="h-full" {...divProps}>
        {renderContent()}
      </div>
    );
  },
  {
    // Static methods to maintain compatibility with SubApp interface
    bus: {
      $on: () => {},
      $off: () => {},
      $emit: () => {},
    },
    setupApp: () => Promise.resolve(),
    preloadApp: () => Promise.resolve(),
    destroyApp: () => Promise.resolve(),
  },
);

export default SubAppIFrame;

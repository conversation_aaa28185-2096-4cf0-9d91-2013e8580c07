import type { useMainViewerService } from '#app/subappManager/service/mainViewer.service';
import SubAppIFrame from '#app/subappManager/view/IFrameViewer/SubAppIFrame';

/**
 * IFrame查看器组件的属性接口
 * Props interface for IFrame viewer component
 */
export interface IWuJieViewerProps {
  /** 主查看器服务实例，包含子应用配置和生命周期方法 */
  /** Main viewer service instance containing sub-app config and lifecycle methods */
  service: ReturnType<typeof useMainViewerService>;
}

/**
 * IFrame查看器组件
 * 用于在IFrame中显示子应用，提供完整的应用容器功能
 *
 * IFrame Viewer Component
 * Used to display sub-applications in an IFrame, providing complete app container functionality
 *
 * 功能特性 / Features:
 * - 子应用IFrame容器 / Sub-app IFrame container
 * - 生命周期管理 / Lifecycle management
 * - 重载功能 / Reload functionality
 * - 响应式布局 / Responsive layout
 *
 * @param service - 主查看器服务，提供子应用配置、重载方法和激活回调
 * @param service - Main viewer service providing sub-app config, reload method and activation callback
 */
const IFrameViewer = ({ service }: IWuJieViewerProps) => {
  return (
    <SubAppIFrame
      // 设置为全屏容器，占满父元素空间
      // Set as full-screen container, filling parent element space
      className="w-full h-full flex-1"
      // 子应用配置，包含URL、名称、属性等信息
      // Sub-app configuration containing URL, name, props, etc.
      app={service.subAppConfig}
      // 重载方法，用于刷新子应用
      // Reload method for refreshing the sub-app
      reload={service.reload}
      // 激活回调，当子应用加载完成时触发
      // Activation callback triggered when sub-app loading completes
      onActivated={service.handleActivated}
    />
  );
};

export default IFrameViewer;

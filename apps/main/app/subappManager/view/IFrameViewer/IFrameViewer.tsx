import type { useMainViewerService } from '#app/subappManager/service/mainViewer.service';
import SubAppIFrame from '#app/subappManager/view/IFrameViewer/SubAppIFrame';

/**
 * IFrame查看器组件的属性接口
 */
export interface IIFrameViewerProps {
  /** 主查看器服务实例，包含子应用配置和生命周期方法 */
  service: ReturnType<typeof useMainViewerService>;
}

/**
 * IFrame查看器组件
 * 用于在IFrame中显示子应用，提供完整的应用容器功能
 *
 * 功能特性:
 * - 子应用IFrame容器
 * - 生命周期管理
 * - 重载功能
 * - 响应式布局
 *
 * @param service - 主查看器服务，提供子应用配置、重载方法和激活回调
 */
const IFrameViewer = ({ service }: IIFrameViewerProps) => {
  return (
    <SubAppIFrame
      // 设置为全屏容器，占满父元素空间
      className="w-full h-full flex-1"
      // 子应用配置，包含URL、名称、属性等信息
      app={service.subAppConfig}
      // 重载方法，用于刷新子应用
      reload={service.reload}
      // 激活回调，当子应用加载完成时触发
      onActivated={service.handleActivated}
    />
  );
};

export default IFrameViewer;

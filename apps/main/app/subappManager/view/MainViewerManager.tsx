import { useMainViewerService } from '#app/subappManager/service/mainViewer.service';
import { SubAppLoading } from '#app/subappManager/component/SubAppLoading';
import { WuJieViewer } from '#app/subappManager/view/WuJieViewer/WuJieViewer';
import IFrameViewer from '#app/subappManager/view/IFrameViewer/IFrameViewer';

/**
 * 主视图管理器组件
 * 负责根据不同条件选择合适的子应用渲染方式
 *
 * 功能特性:
 * - 自动检测页面准备状态
 * - 根据URL参数选择渲染方式
 * - 支持无界微前端和IFrame两种模式
 * - 提供统一的加载状态管理
 */
const MainViewerManager = () => {
  // 获取主视图服务实例
  const service = useMainViewerService();

  /**
   * 渲染内容函数
   * 根据页面状态和URL参数决定使用哪种渲染方式
   *
   * 渲染逻辑:
   * 1. 检查页面是否准备就绪（pageShow && url）
   * 2. 未准备就绪时显示加载组件
   * 3. 检查URL是否包含wujie参数
   * 4. 有wujie参数使用WuJieViewer，否则使用IFrameViewer
   */
  const renderContent = () => {
    // 获取子应用配置URL
    const url = service.subAppConfig.url;

    // 检查页面是否准备就绪
    const isReady = service.pageShow && url;

    // 页面未准备就绪时显示加载状态
    if (!isReady) {
      return <SubAppLoading />;
    }

    /**
     * 检测URL查询参数中是否包含wujie参数
     * 用于判断是否使用无界微前端模式
     */
    const hasWujieQuery = (() => {
      let hasWujieParam = false;
      const uri = new URL(url);

      // 遍历所有查询参数
      uri.searchParams.forEach((value, key) => {
        if (key === 'wujie') {
          hasWujieParam = true;
        }
      });

      return hasWujieParam;
    })();

    // 根据是否有wujie参数选择渲染方式
    if (hasWujieQuery) {
      // 使用无界微前端渲染器
      return <WuJieViewer service={service} />;
    }

    // 默认使用IFrame渲染器
    return <IFrameViewer service={service} />;
  };

  // 返回渲染内容
  return renderContent();
};

export default MainViewerManager;

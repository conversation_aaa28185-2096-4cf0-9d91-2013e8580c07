import type { HTMLAttributes } from 'preact/compat';
import type { startOptions } from 'wujie';
import { useRef, useState, useEffect, useCallback } from 'preact/hooks';
import { AboutLauncherTracking } from '@ssglauncher/tracking';
import { ConnectFailed } from '../component/ConnectFailed';
import { logger } from '#common/util/logger';
import loading from '../assets/Loading.webp';

const images = {
  loading,
};

// 其背后实现都是 lifecycle，随便选一个都一样
type TLifeCycle = startOptions['activated'];

interface SubAppIFrameProps extends HTMLAttributes<HTMLDivElement> {
  app: Omit<startOptions, 'el'>;
  reload?: () => void;
  onActivated?: TLifeCycle;
  onDeactivated?: TLifeCycle;
}

// Static methods to maintain compatibility with SubApp interface
const SubAppIFrame = Object.assign(
  (props: SubAppIFrameProps) => {
    const { app, onActivated, onDeactivated, className, style, ...divProps } = props;

    const containerRef = useRef<HTMLDivElement>(null);
    const iframeRef = useRef<HTMLIFrameElement>(null);
    const loadTimeoutRef = useRef<NodeJS.Timeout>();
    const isDestroyedRef = useRef(false);

    const [isError, setIsError] = useState(false);
    const [isLoading, setIsLoading] = useState(true);

    const loadError = useCallback((url: string, e: Error) => {
      console.error('页面连接失败，错误：%s，连接地址：%s', e, url);
      if (!isDestroyedRef.current) {
        debugger;
        setIsError(true);
        setIsLoading(false);
      }
    }, []);

    const handleIFrameLoad = useCallback(() => {
      alert('iframe loaded');
      debugger;
      if (!isDestroyedRef.current) {
        setIsLoading(false);
        setIsError(false);
        console.log('iframe load finish');

        // Call lifecycle callbacks
        if (onActivated) {
          onActivated(null);
        }
      }
    }, [onActivated]);

    const handleIFrameError = useCallback(() => {
      if (!isDestroyedRef.current) {
        loadError(app.url, new Error('IFrame load failed'));
      }
    }, [app.url, loadError]);

    const reload = useCallback(() => {
      if (iframeRef.current) {
        setIsError(false);
        setIsLoading(true);
        iframeRef.current.src = app.url;
      } else {
        window.location.reload();
      }

      new AboutLauncherTracking((p: any) => (window as any).xcef.dataAnalysisModule.report(p)).ssgReloadEvent({
        reloadSource: app.url,
      });
    }, [app.url]);

    const start = useCallback(async () => {
      logger('[SubAppIFrame]', { url: app.url });

      try {
        // Set a timeout for iframe loading
        loadTimeoutRef.current = setTimeout(() => {
          if (isLoading && !isDestroyedRef.current) {
            loadError(app.url, new Error('IFrame load timeout'));
          }
        }, 30000); // 30 second timeout

        // The iframe will handle the actual loading
        // State will be updated via handleIFrameLoad or handleIFrameError
      } catch (error) {
        console.error('iframe fetch 异常：%s', error);
        if (!isDestroyedRef.current) {
          debugger;
          setIsError(true);
          setIsLoading(false);
        }
      }
    }, [app.url, isLoading, loadError]);

    // Mount effect
    useEffect(() => {
      start();
    }, []);

    // Update effect
    useEffect(() => {
      setIsError(false);
      setIsLoading(true);
      start();
    }, [app.name, app.url]);

    // Cleanup effect
    useEffect(() => {
      return () => {
        isDestroyedRef.current = true;
        if (loadTimeoutRef.current) {
          clearTimeout(loadTimeoutRef.current);
        }
        console.log('iframe destroy');

        // Call deactivated callback on unmount
        if (onDeactivated) {
          onDeactivated(null);
        }
      };
    }, [onDeactivated]);

    const renderLoading = () => {
      debugger;
      if (isLoading) {
        return (
          <div className="absolute inset-0 flex items-center justify-center z-10">
            <div className="flex items-center justify-center -mt-0px">
              <img src={images.loading} alt="Loading..." />
            </div>
          </div>
        );
      }
      return null;
    };

    const renderContent = () => {
      if (isError) {
        return <ConnectFailed onClick={reload} />;
      }
      return (
        <div className={`${className || ''} relative h-full`} style={style}>
          {renderLoading()}
          {/* eslint-disable-next-line react/iframe-missing-sandbox */}
          <iframe
            ref={iframeRef}
            src={app.url}
            className="w-full h-full border-0"
            onLoad={handleIFrameLoad}
            onError={handleIFrameError}
            sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-modals allow-top-navigation"
            title={app.name}
          />
        </div>
      );
    };

    useEffect(() => {
      onActivated(null);
    }, []);

    return (
      <div ref={containerRef} className="h-full" {...divProps}>
        {renderContent()}
      </div>
    );
  },
  {
    // Static methods to maintain compatibility with SubApp interface
    bus: {
      $on: () => {},
      $off: () => {},
      $emit: () => {},
    },
    setupApp: () => Promise.resolve(),
    preloadApp: () => Promise.resolve(),
    destroyApp: () => Promise.resolve(),
  },
);

export default SubAppIFrame;

import type { useMainViewerService } from '../../service/mainViewer.service';
import { useEffect, useRef } from 'preact/hooks';
import { ErrorBoundary } from '../../component/ErrorBoundary';
import SubApp from '#app/subappManager/view/WuJieViewer/SubApp';

export interface IWuJieViewerProps {
  service: ReturnType<typeof useMainViewerService>;
}

export function WuJieViewer({ service }: IWuJieViewerProps) {
  const ref = useRef(null);

  useEffect(() => {
    let timer = setTimeout(
      () => {
        if (service.pageShow && service.subAppConfig.url) {
          checkSubAppMounted();
        }
      },
      import.meta.env.DEV ? 30_000 : 3000,
    );

    return () => {
      clearTimeout(timer);
    };
  }, [service.subAppConfig?.url, checkSubAppMounted]);

  /** 检查子应用是否正常挂载 (子应用可能因wujie/vite-plugin-ssr的原因低概率挂载失败) */
  function checkSubAppMounted() {
    const r: HTMLElement = ref.current?.base?.firstChild;
    if (!r) {
      console.log('vvv no SubApp ref');
      reloadMainApp();
      return;
    }
    const childEle = r.firstChild as HTMLIFrameElement;
    const child = (childEle?.shadowRoot ?? childEle?.contentDocument) as any;

    console.log('vvv shadowRoot or contentDocument: ', child);
    const root = child?.querySelector('#root>div');
    console.log('vvv root', root);
    if (!root) {
      console.log('vvv no root');
      reloadMainApp();
    }
  }

  function reloadMainApp() {
    window.location.reload();
  }

  const renderSubApp = () => {
    return (
      <SubApp
        ref={ref}
        className="w-full h-full flex-1"
        app={service.subAppConfig}
        reload={service.reload}
        onActivated={service.handleActivated}
        onDeactivated={service.handleDeactivated}
      />
    );
  };

  return <ErrorBoundary>{renderSubApp()}</ErrorBoundary>;
}

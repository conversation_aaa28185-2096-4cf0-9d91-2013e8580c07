import { useEffect, useRef } from 'preact/hooks';
import { useMainViewerService } from '../../service/mainViewer.service';
import { ErrorBoundary } from '../../component/ErrorBoundary';
import { SubAppLoading } from '../../component/SubAppLoading';
import SubApp from '#app/subappManager/view/WuJieViewer/SubApp';
import SubAppIFrame from '#app/subappManager/view/IFrameViewer/SubAppIFrame';

export interface IMainViewerProps {
  service: ReturnType<typeof useMainViewerService>;
}

export function WuJieViewer({ service }: IMainViewerProps) {
  const ref = useRef(null);

  useEffect(() => {
    let timer = setTimeout(
      () => {
        if (service.pageShow && service.subAppConfig.url) {
          checkSubAppMounted();
        }
      },
      import.meta.env.DEV ? 30_000 : 3000,
    );

    return () => {
      clearTimeout(timer);
    };
  }, [service.subAppConfig?.url, checkSubAppMounted]);

  /** 检查子应用是否正常挂载 (子应用可能因wujie/vite-plugin-ssr的原因低概率挂载失败) */
  function checkSubAppMounted() {
    const r: HTMLElement = ref.current?.base?.firstChild;
    if (!r) {
      console.log('vvv no SubApp ref');
      reloadMainApp();
      return;
    }
    const childEle = r.firstChild as HTMLIFrameElement;
    const child = (childEle?.shadowRoot ?? childEle?.contentDocument) as any;

    console.log('vvv shadowRoot or contentDocument: ', child);
    const root = child?.querySelector('#root>div');
    console.log('vvv root', root);
    if (!root) {
      console.log('vvv no root');
      reloadMainApp();
    }
  }

  function reloadMainApp() {
    window.location.reload();
  }

  const renderSubApp = () => {
    const url = service.subAppConfig.url || 'http://127.0.0.1:5173/games/rank?name=Mecha+BREAK&code=mechabreak_gpqa';
    if (service.pageShow && url) {
      const enableIframeMode = !service.subAppConfig.url.includes('wujie');
      if (enableIframeMode) {
        return (
          <SubAppIFrame
            ref={ref}
            className="w-full h-full flex-1"
            app={service.subAppConfig}
            reload={service.reload}
            onActivated={service.handleActivated}
          />
        );
      }
      return (
        <SubApp
          ref={ref}
          className="w-full h-full flex-1"
          app={service.subAppConfig}
          reload={service.reload}
          onActivated={service.handleActivated}
          onDeactivated={service.handleDeactivated}
        />
      );
    }
    return <SubAppLoading />;
  };

  return <ErrorBoundary>{renderSubApp()}</ErrorBoundary>;
}

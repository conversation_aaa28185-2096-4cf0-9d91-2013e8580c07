import type { Observable } from 'rxjs';
import type { IGameBuyStatusRepo } from '#domains/gameManager/repository/gameBuyStatus.repo';
import { BSRepository, InjectPlugin, Repository } from '@banshee/ex-banshee';
import {
  BehaviorSubject,
  ReplaySubject,
  catchError,
  from,
  of,
  shareReplay,
  switchMap,
  takeWhile,
  lastValueFrom,
  tap,
} from 'rxjs';
import { type XCefSDK, type IGameBuyStatus, promisify } from '@ssglauncher/sdk';
import { listenerToObserver } from '@ssglauncher/operators';
import { XCefSDKSymbol } from '#domains/infra/xcefSdk.plugin';

type TXCef = Awaited<ReturnType<typeof XCefSDK>>;

type GameBuyStatus = Partial<IGameBuyStatus>;

@Repository('GameBuyStatusManager')
export class GameBuyStatusManagerRepo extends BSRepository implements IGameBuyStatusRepo {
  private xcef$: ReplaySubject<TXCef> = new ReplaySubject(1);
  private gameBuyStatusFromListener$: BehaviorSubject<GameBuyStatus> = new BehaviorSubject({});
  /** 监听购买状态的开关 */
  private statusListenerSwitch$: BehaviorSubject<boolean> = new BehaviorSubject(false);

  constructor(
    @InjectPlugin(XCefSDKSymbol)
    xcefPlugin: typeof XCefSDK,
  ) {
    super();
    this.init(xcefPlugin);
  }

  getGameBuyStatus(gameId: string) {
    return this.ready(xcef =>
      this.statusListenerSwitch$.pipe(
        takeWhile(() => !!gameId),
        switchMap<boolean, Observable<IGameBuyStatus>>(listenerSwitch => {
          if (!listenerSwitch) {
            const getGameBuyStatusAsync = promisify(xcef.gameModuleData.getGameBuyStatus.bind(xcef.gameModuleData));
            return from(getGameBuyStatusAsync({ appCode: gameId }) as Promise<IGameBuyStatus>);
          }
          const listener = this.gameBuyStatusFromListener$.pipe(
            switchMap(item => {
              if (item.appCode === gameId) {
                return of(item) as Observable<IGameBuyStatus>;
              }
              const getGameBuyStatusAsync = promisify(xcef.gameModuleData.getGameBuyStatus.bind(xcef.gameModuleData));
              return from(getGameBuyStatusAsync({ appCode: gameId }) as Promise<IGameBuyStatus>);
            }),
          );
          return listener;
        }),
        tap(gameBuyStatus => {
          if (gameBuyStatus.isBuyToPlayGame && !this.statusListenerSwitch$.value) {
            // 只有在买断制游戏且从未创建过监听时，才创建监听，减小长连接的数量来减小对服务端的压力
            this.createGameBuyStatusListener();
            this.statusListenerSwitch$.next(true);
          }
        }),
        catchError(err => {
          console.log(err);
          return JSON.parse('{}');
        }),
      ),
    ) as Observable<IGameBuyStatus>;
  }

  async openGameBuyLink(gameId: string) {
    const xcef = await lastValueFrom(this.xcef$);
    xcef.gameModule.openGameBuyLink({
      appCode: gameId,
    });
  }

  private ready<T>(observer: (xcef: TXCef) => Observable<T>): Observable<T> {
    return this.xcef$.pipe(
      shareReplay(1),
      switchMap(xcef => observer(xcef)),
    );
  }

  private createGameBuyStatusListener() {
    this.ready(xcef => listenerToObserver(xcef.gameModule.onGameBuyStatusChanged)).subscribe(
      this.gameBuyStatusFromListener$,
    );
  }

  private init(xcefPlugin: typeof XCefSDK) {
    from(xcefPlugin()).subscribe(this.xcef$);
  }
}

export { render };
// See https://vite-plugin-ssr.com/data-fetching
export const passToClient = ['pageProps'];

import { prerender } from 'preact-iso';
import { PageShell } from './PageShell';
import { escapeInject, dangerouslySkipEscape } from 'vite-plugin-ssr/server';
import type { PageContextServer } from './types';

async function render(pageContext: PageContextServer) {
  const { Page, pageProps } = pageContext;
  const { html: pageHtml } = await prerender(
    <PageShell pageContext={pageContext}>{Page ? <Page {...pageProps} /> : ''}</PageShell>,
  );

  // See https://vite-plugin-ssr.com/head
  const { documentProps } = pageContext.exports;
  const title = documentProps?.title || 'Welcome to additional';
  const desc = documentProps?.description || '';

  const documentHtml = escapeInject`<!DOCTYPE html>
    <html lang="en">
      <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="description" content="${desc}" />
        <title>${title}</title>
      </head>
      <body>
        <div id="root">${dangerouslySkipEscape(pageHtml)}</div>
      </body>
    </html>`;

  return {
    documentHtml,
    pageContext: {
      // We can add some `pageContext` here, which is useful if we want to do page redirection https://vite-plugin-ssr.com/page-redirection
    },
  };
}

import { getLang } from '@ssglauncher/utils';
import { getAppEnv } from '#app/common/utils/appEnv';
import { GP_ACCOUNT_SDK_LANGUAGE_MAP } from '#app/common/utils/language';

/** 本地静态的注册地址 */
export function getStaticRegistryUrl(): string {
  const envConfig = getAppEnv();
  const langKey = GP_ACCOUNT_SDK_LANGUAGE_MAP[getLang()];
  return `${envConfig.SSG_INTL_ACCOUNT_PREFIX}/${langKey}/#/sign-up`;
}

export function getForgotPassword(): string {
  const envConfig = getAppEnv();
  const langKey = GP_ACCOUNT_SDK_LANGUAGE_MAP[getLang()];
  return `${envConfig.SSG_INTL_ACCOUNT_PREFIX}/${langKey}/#/forgot-password`;
}

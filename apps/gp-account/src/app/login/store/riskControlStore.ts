import { useSessionStorageState } from 'ahooks';
import { createStoreKey } from '#app/common/utils/store';

/** 安全验证页面接口报错的风险信息数据 */
interface RiskInfo {
  type: 'login' | 'bind' | 'client';
  account: string;
  /** ticket */
  serial: string;
  /** 设备名 */
  device: string;
  /** 触发风控的时间 */
  time: string;
  /** 实际上是密码 */
  uid: string;
  maskAccount?: string;
}

/** 客户端传入的风险信息数据 */
interface RiskControlDataFromClient {
  type: 'client';
  account: string;
  device: string;
  serial: string;
  time: string;
  maskAccount: string;
  loginType: number;
  isAuthRemember: number;
}

/** 安全风控流程相关数据 */
interface RiskControlData extends RiskInfo {
  loginType?: number;
  isAuthRemember?: number;
  accountHash: string;
}

function createFormData(): RiskControlData {
  return {
    time: '',
    device: '',
    serial: '',
    account: '',
    type: 'login',
    uid: '',
    loginType: 0,
    maskAccount: '',
    isAuthRemember: 0,
    accountHash: '',
  };
}

/** 整个手机绑定流程的全局store  */
export function useRiskControlStore() {
  const [state, setState] = useSessionStorageState('riskControlData', { defaultValue: createFormData() });

  function reset() {
    setState(createFormData);
  }

  /** 设置客户端传入的安全验证页面数据 */
  function setDataFromQuery(query: Pick<RiskControlData, 'accountHash' | 'loginType' | 'isAuthRemember'>) {
    setState(prev => ({
      ...(prev as RiskControlData),
      ...query,
    }));
  }

  /** 设置安全验证页面接口报错的风险信息数据 */
  function setRiskInfo(data: RiskInfo) {
    setState(prev => ({
      ...(prev as RiskControlData),
      ...data,
    }));
  }

  /** 设置客户端传入的风险信息数据 */
  function setRiskControlDataFromClient(data: RiskControlDataFromClient) {
    setState(prev => ({
      ...(prev as RiskControlData),
      ...data,
    }));
  }

  return {
    state,
    reset,
    setDataFromQuery,
    setRiskInfo,
    setRiskControlDataFromClient,
  };
}

export type RiskControlStore = ReturnType<typeof useRiskControlStore>;
export const RISK_CONTROL_STORE_KEY = createStoreKey<RiskControlStore>();

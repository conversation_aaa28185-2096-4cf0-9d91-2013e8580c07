import type { useVerificationCodeLoginService } from '#app/login/services/ui/verificationCodeLogin.service';
import { AccountField, InputEnhance, TextButton, ConfirmButton } from '@ssglauncher/account/components';
import { useTranslation } from 'react-i18next';
import { Controller } from 'react-hook-form';
import { Fragment, Suspense } from 'preact/compat';
import { Tooltip } from '@ssglauncher/components';
import { Icon } from '@ssglauncher/iconfont';
import { ThirdPartyLogin } from '#app/login/views/AccountLogin/ThirdPartyLogin';

export function VerifyVerificationCode({ service }: { service: ReturnType<typeof useVerificationCodeLoginService> }) {
  const TimerConfirmButton = service.Timer;
  const AutoLoginCheckbox = service.AutoLoginCheckbox;
  const { t } = useTranslation();
  return (
    <Fragment>
      <form className="relative flex flex-col w-full mt-sp32" onSubmit={service.onSubmit}>
        <div className="relative">
          <Controller
            name="account"
            rules={service.accountRules}
            control={service.control}
            render={({ field, fieldState: { error } }) => (
              <AccountField
                {...field}
                error={error?.message || ''}
                errorVisible={false}
                type="text"
                placeholder={t('ACCOUNT:VERIFICATION_CODE_LOGIN.ACCOUNT_INPUT.PLACEHOLDER', '邮箱验证')}
                maxLength={50}
                showTips={service.showLastLoggedinTips}
                tips={t('ACCOUNT:LOGIN.LAST_LOGIN_ACCOUNT', '上次登录邮箱')}
              />
            )}
          />
        </div>
        <div className="flex justify-between items-center mt-sp20">
          <div className="mr-sp8 min-w-[180px]">
            <Controller
              name="code"
              control={service.control}
              rules={service.verificationCodeRules}
              render={({ field, fieldState: { error } }) => (
                <InputEnhance
                  {...field}
                  error={!!error?.message}
                  icon="secure"
                  type="text"
                  placeholder={t('ACCOUNT:VERIFICATION_CODE_LOGIN.CODE_INPUT.PLACEHOLDER', '请输入验证码')}
                  maxLength={6}
                />
              )}
            />
          </div>
          <Suspense fallback={null}>{TimerConfirmButton}</Suspense>
        </div>
        <div className="w-full flex justify-between mt-12px">
          <div className="flex items-center relative">
            <AutoLoginCheckbox />
            <Tooltip
              content={t('ACCOUNT:LOGIN.AUTO_LOGIN_TOOL_TIPS', '启动器会自动登录并保持登录状态，请谨慎选择')}
              placement="bottom"
              variant="normal"
              className="flex items-center"
              contentClassName="w-224px left-12%"
            >
              <Icon icon="about" className="ml-sp4 text-white-40 text-title hover:text-white-100" />
            </Tooltip>
          </div>
        </div>
        <div className="h-sp36 text-error text-caption">
          <div className="pt-sp20">{service.errorMessage}</div>
        </div>
        <div className="w-full">
          <ConfirmButton disabled={!service.loginBtnStatus.isClickable}>
            {t('ACCOUNT:LOGIN.LOGIN_BUTTON.TEXT', '立即登录')}
          </ConfirmButton>
        </div>
      </form>
      <ThirdPartyLogin className="mt-50px" />
      <div className="mt-50px w-full flex justify-center">
        <TextButton text={t('ACCOUNT:LOGIN.GO_TO_REGISTER', '前往注册')} onClick={service.goToRegistry} />
      </div>
    </Fragment>
  );
}

import type { ServerError } from '#app/common/models/serverError';
import {
  Register<PERSON><PERSON>,
  SMSApi,
  EChannel,
  EVerifyType,
  VerifyCodeDTO,
  ValidateVerifyCodeDTO,
} from '@ssglauncher/server-api';
import { useCallback, useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { secretStr } from '@ssglauncher/utils';
import { AccountRegisterTracking, AccountOperationTracking } from '@ssglauncher/tracking';
import { CaptchaVerifyState, useCaptcha, useXCefUtil } from '@ssglauncher/hooks';
import { createTopToast } from '@ssglauncher/components';
import { isNetworkError } from '@ssglauncher/utils';
import { useLoading } from '../../common/hooks/useLoading';
import { usePolicyChecker } from '../../common/hooks/usePolicyChecker';
import { useFlash } from '../../common/hooks/useFlash';

import { initRequest } from '#app/common/utils/request';
import { useRouter } from '#app/common/hooks/useRouter';
import { REGISTER_STORE_KEY } from '#app/register/store/registryStore';
import { useStore } from '#app/common/utils/store';

interface FormDto {
  account: string;
  contactWay: string;
  password: string;
  verifyCode: string;
}

export const tabTransMap: Record<'phone' | 'normal', string> = {
  normal: '普通注册',
  phone: '手机注册',
};

export function useRegisterService() {
  const toast = createTopToast();
  const router = useRouter();
  const verify = useCaptcha('register');
  const xCefUtil = useXCefUtil();
  const registerStore = useStore(REGISTER_STORE_KEY);
  const opTracking = new AccountOperationTracking((p: any) => xCefUtil?.dataAnalysisModule.report(p));
  const regTracking = new AccountRegisterTracking((p: any) => xCefUtil?.dataAnalysisModule.report(p));

  const [isAllowPolicy, PolicyCheckbox] = usePolicyChecker();
  const [smsIsDisabled, setSendSmsDisabled] = useState(false);
  const [currentTab, setTab] = useState<'phone' | 'normal'>('phone');
  const [privacyPhone, setPrivacyPhone] = useState('');
  const [flashRef, flashAction] = useFlash();

  const {
    control,
    handleSubmit,
    trigger,
    getValues,
    formState: { errors, isDirty },
    watch,
    getFieldState,
    setError,
    setFocus,
    reset,
  } = useForm<FormDto>({
    defaultValues: {
      account: '',
      contactWay: '',
      password: '',
      verifyCode: '',
    },
  });

  const [registerAction, loading] = useLoading(async (data: FormDto) => {
    // 这个页面不操作注册，仅验证验证是否正确
    const request = await initRequest();
    const smsApi = new SMSApi(request);

    const { password, ...restData } = data;

    try {
      // 验证
      const dto = new ValidateVerifyCodeDTO({
        channel: EChannel.REGISTER,
        verifyType: EVerifyType.SMS,
        ...restData,
      });
      await smsApi.validateSMSVerifyCode(dto);
      registerStore.setRegisterFormData({
        ...data,
        privacyPhone,
        password: password,
        type: currentTab,
      });
      router.push('/register/identify');
    } catch (e) {
      if (isNetworkError(e)) {
        return;
      }
      const error = e as ServerError;
      toast(error?.msg);

      // 上报数据
      regTracking.ssgRegisterAccountFail({
        preAccountId: secretStr(currentTab === 'normal' ? data.account : data.contactWay),
        regWay: tabTransMap[currentTab] as never,
        code: error?.code,
        statusReason: error?.msg,
        phoneNumber: secretStr(privacyPhone),
      });
    }
  });

  // 进来聚焦账号
  useEffect(() => {
    setFocus('contactWay');
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 错误信息
  const errorMessage = Object.keys(errors).map(key => errors[key]?.message ?? '');

  // 获取验证码按钮状态
  const mobile = watch().contactWay;
  useEffect(() => {
    setSendSmsDisabled(!mobile);
  }, [getFieldState, mobile]);

  const handleTabSelect = (val: 'phone' | 'normal') => {
    setTab(val);
    reset();
  };

  const fetchVerifyCode = async () => {
    const request = await initRequest();
    const registerApi = new RegisterApi(request);
    const smsApi = new SMSApi(request);
    const isNormal = currentTab === 'normal';

    const mobileCheck = await trigger('contactWay');
    let accountCheck = true;

    // 如果在普通注册 需要检查账号有没有填
    if (isNormal) {
      accountCheck = await trigger('account');
    }

    if (!mobileCheck || !accountCheck) {
      return Promise.reject();
    }

    const mobile = getValues('contactWay');
    const account = getValues('account');
    try {
      const geeData = await verify();

      if (geeData.state !== CaptchaVerifyState.Pass) {
        return Promise.reject();
      }

      // 且检查的是账号重复而非手机重复
      const isExist = await registerApi.isUserExist({
        account: isNormal ? account : mobile,
      });

      if (isExist) {
        if (isNormal) {
          setError('account', { message: '该账号已被注册' });
        } else {
          setError('contactWay', { message: '该手机号已被注册' });
        }

        // 上报数据
        regTracking.ssgRegisterAccountFail({
          preAccountId: secretStr(isNormal ? account : mobile),
          regWay: tabTransMap[currentTab] as never,
          code: -99998,
          statusReason: '账号已被注册',
          phoneNumber: secretStr(mobile),
        });

        return Promise.reject();
      }
      const dto = new VerifyCodeDTO({
        account: isNormal ? account : mobile,
        contactWay: mobile,
        verifyType: EVerifyType.SMS,
        captcha: geeData.data,
        channel: EChannel.REGISTER,
      });

      const phoneRes = await smsApi.getSMSVerifyCode(dto);

      setPrivacyPhone(phoneRes.contactWay);
    } catch (e) {
      if (isNetworkError(e)) {
        return Promise.reject();
      }
      const error = e as ServerError;
      toast(error?.msg);

      // 上报数据
      regTracking.ssgRegisterAccountFail({
        preAccountId: secretStr(isNormal ? account : mobile),
        regWay: tabTransMap[currentTab] as never,
        code: error?.code,
        statusReason: error?.msg,
        phoneNumber: secretStr(mobile),
      });
      return Promise.reject(error);
    }
  };

  const onSubmit = async (data: FormDto) => {
    // 上报数据
    // 这个时机上报目的：可以收集到不同意协议的行为
    regTracking.ssgRegisterAccountSubmitted({
      preAccountId: secretStr(currentTab === 'normal' ? data.account : data.contactWay),
      regWay: tabTransMap[currentTab] as never,
      phoneNumber: secretStr(data.contactWay),
      isAgreeDeal: isAllowPolicy ? '1' : '0',
    });

    if (!isAllowPolicy) {
      flashAction();

      // 上报数据
      regTracking.ssgRegisterAccountFail({
        preAccountId: secretStr(currentTab === 'normal' ? data.account : data.contactWay),
        regWay: tabTransMap[currentTab] as never,
        code: -99999,
        statusReason: '未同意协议',
        phoneNumber: secretStr(data.contactWay),
      });

      return;
    }

    await registerAction(data);
  };

  const switchLoginTracking = useCallback(
    (val: string) => () => {
      // 上报数据
      opTracking.ssgChooseLogin({
        loginWay: val,
      });
    },
    [opTracking],
  );

  return {
    control,
    PolicyCheckbox,
    errorMessage,
    isDirty,
    smsIsDisabled,
    currentTab,
    loading,
    flashRef,

    onSubmit: handleSubmit(onSubmit),
    fetchVerifyCode,
    handleTabSelect,
    switchLoginTracking,
  };
}

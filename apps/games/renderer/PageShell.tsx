import '#common/utils/init-arms';
import type { JSX } from 'preact/jsx-runtime';
import type { PageContext } from './types';
import type { Banshee } from '@banshee/ex-banshee';
import { Suspense, useEffect, useState } from 'react';
import { I18nextProvider } from 'react-i18next';
import { BansheeProvider } from '@banshee/banshee-preact';
import { applyDeviceInDocument } from '@ssglauncher/utils';
import { PageContextProvider } from './usePageContext';
import './PageShell.css';
import i18n from './i18n';
import { createBanshee } from '#common/utils/container';
import { PageLoading } from '#common/components/PageLoading';

export { PageShell };

function PageShell({ children, pageContext }: { children: JSX.Element | string; pageContext: PageContext }) {
  const [banshee, setBanshee] = useState<Banshee>();

  if (!import.meta.env.SSR) {
    i18n.changeLanguage(window.__XCEF_LANG__);
  }

  useEffect(() => {
    setBanshee(createBanshee());
    applyDeviceInDocument();
  }, []);

  return (
    <PageContextProvider pageContext={pageContext}>
      <Suspense fallback={<PageLoading />}>
        <BansheeProvider banshee={banshee!}>
          <I18nextProvider i18n={i18n}>{children}</I18nextProvider>
        </BansheeProvider>
      </Suspense>
    </PageContextProvider>
  );
}

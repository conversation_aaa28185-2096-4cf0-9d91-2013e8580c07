/**
 * 从无界微前端框架中获取props
 * @returns 如果可用则返回无界的props对象，否则返回undefined
 */
const getWujieProps = () => {
  return (window as any).$wujie?.props;
};

/**
 * 在iframe中运行时从URL搜索参数中获取props
 * @returns 如果找到则返回URL参数中的props字符串，否则返回null
 */
const getIFrameProps = () => {
  const url = new URL(window.location.href);
  const $propsQs = url.searchParams.get('$props');
  if ($propsQs) {
    return JSON.parse($propsQs);
  }
  return null;
};

/**
 * 尝试使用多种策略从父级上下文中获取props
 *
 * 此函数尝试不同的方法来获取从父应用程序传递的props：
 * 1. 首先尝试从无界微前端框架获取props
 * 2. 回退到检查基于iframe嵌入的URL参数
 * 3. 如果没有找到props则返回null
 *
 * @returns 来自父级上下文的Props对象，如果未找到则返回null
 */
export const getParentProps = () => {
  // 首先尝试从无界微前端框架获取props
  const wujieProps = getWujieProps();
  if (wujieProps) {
    return wujieProps;
  }

  // 回退到从URL参数获取iframe的props
  const iframeProps = getIFrameProps();
  if (iframeProps) {
    return iframeProps;
  }

  // 从任何来源都没有找到props
  return null;
};

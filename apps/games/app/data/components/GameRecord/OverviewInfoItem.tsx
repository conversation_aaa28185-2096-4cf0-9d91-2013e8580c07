import { clsx } from 'clsx';
interface OverviewInfoItemProps {
  label: string;
  value: any;
  className?: string;
}

export function OverviewInfoItem({ label, value, className }: OverviewInfoItemProps) {
  return (
    <div className={clsx('h-auto', className)}>
      <div className="text-mbBrand-60 text-body">{label}</div>
      <div className="text-white-80 text-head leading-18px mt-4px font-mecha">{value}</div>
    </div>
  );
}

import { useTranslation } from 'react-i18next';
import { useMemo } from 'preact/hooks';
import { isChannel, ChannelKey } from '@ssglauncher/utils';

export function useGameRecordChannelConfigService(channel = '', loading = false) {
  const { t } = useTranslation();
  const isSteamChannel = useMemo(() => isChannel(channel, ChannelKey.Steam), [channel]);

  const gameRecordLoginTips = useMemo(() => {
    if (isSteamChannel) {
      return t('GAMES:ZONE.GAME_RECORD.MASK.GET_ROLE_TIPS', '获取角色信息后展示战绩');
    }
    return t('GAMES:ZONE.GAME_RECORD.LOGIN_TIPS', '登录后即可查看战绩');
  }, [t, isSteamChannel]);

  const gameRecordLoginText = useMemo(() => {
    if (isSteamChannel) {
      return loading
        ? t('GAMES:ZONE.GAME_RECORD.MASK.GET_ROLE_BUTTON_LOADING_TEXT', '获取中')
        : t('GAMES:ZONE.GAME_RECORD.MASK.GET_ROLE_BUTTON_TEXT', '立即获取');
    }
    return t('GAMES:ZONE.GAME_RECORD.LOGIN_BUTTON_TEXT', '立即登录');
  }, [t, isSteamChannel, loading]);

  return {
    gameRecordLoginTips,
    gameRecordLoginText,
  };
}

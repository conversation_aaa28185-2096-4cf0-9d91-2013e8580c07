import type { Option } from '#common/models/options.model';
import type { GameSeason } from '#domains/zone/model/gameRecord';
import type { PlayType as MatchMode } from '@ssglauncher/server-api';
import { useCallback, useEffect, useMemo } from 'react';
import { useService, useStream } from '@banshee/banshee-preact';
import { useTranslation } from 'react-i18next';
import { getMatchModeOptions } from '../utils/matchMode';
import { GameRecordTab, type GameRecordTabModel } from '#app/data/models/gameRecordTab';
import { GameRecordService } from '#domains/zone/service/gameRecord.service';
import { UserService } from '#domains/account/service/user.service';
import { getSeasonDataTab, getGameRecordTab } from '#app/data/constants/gameRecordTab';
import { useInitialLoading } from '#common/hooks/useInitialLoading';
import { defaultState } from '#app/data/models/gameRecordPageState';
import { ChannelService } from '#domains/channel/service/channel.service';
import { MbServerListService } from '#domains/zone/service/mbServerList.service';
import { useGameCode } from '#common/hooks/useGameCode';
import { getParentProps } from '#app/data/utils/getParentProps';

export function useGameRecordService() {
  const [t, { language }] = useTranslation();
  const gameRecordService = useService(GameRecordService);
  const userService = useService(UserService);
  const channelService = useService(ChannelService);
  const configData = useStream(() => channelService.getConfig());
  const appCode = useGameCode();
  const mbServerListService = useService(MbServerListService);
  useStream(() => mbServerListService.fetchMbServerList(appCode)); // 战绩页初始化区服列表
  const selectedServer = useStream(() => mbServerListService.getSelectedServer());
  const userData = useStream(() => userService.getUserData(), { account: '', accountOpenId: '' });
  const gameRecordPageState = useStream(() => gameRecordService.gameRecordPageStateSource());

  const showLoginMask = useMemo(() => {
    const showAfterLogin = getParentProps()?.showAfterLogin;
    return showAfterLogin && !userData.accountOpenId;
  }, [userData.accountOpenId]);

  const tabs: GameRecordTabModel[] = [getSeasonDataTab(), getGameRecordTab()];

  const queryParams = useMemo(() => {
    console.log('[MBServer] [games] selectedServer.key', selectedServer?.key);
    return {
      server: selectedServer?.key,
    };
  }, [selectedServer?.key]);
  const seasonOptions = useStream(props => gameRecordService.querySeasonOptions(props), [], [queryParams]);

  const isAccountFetching = useStream(() => userService.isFetching(), false);
  const isSeasonListFetching = useStream(() => gameRecordService.isSeasonListFetching(), false);
  const isSeasonDataFetching = useStream(() => gameRecordService.isSeasonDataFetching(), false);
  const isLoading = useInitialLoading(isAccountFetching, isSeasonListFetching, isSeasonDataFetching);

  const allMatchModeOptions: Option<MatchMode>[] = useMemo(
    () =>
      getMatchModeOptions().map(item => ({
        value: item.value,
        label: t(item.intlKey),
      })),
    [t, language],
  );
  const matchModeOptions: Option<MatchMode>[] = useMemo(() => {
    if (!configData?.Config?.gameMode) {
      return allMatchModeOptions;
    }
    return configData.Config.gameMode
      .split(',')
      .map(mode => {
        const option = allMatchModeOptions.find(item => item.value === Number(mode));
        return option as Option<MatchMode>;
      })
      .filter(item => !!item);
  }, [configData?.Config?.gameMode, allMatchModeOptions]);

  const onChangeMatchMode = useCallback(
    (option: Option) => {
      gameRecordService.setManualGameRecordPageState({
        matchMode: option.value,
        page: 1,
        matchId: '',
      });
      gameRecordService.gameRecordModeClicked(appCode, option.label);
    },
    [appCode],
  );

  type SeasonOption = Option<GameSeason['id']>;
  const onChangeSeason = useCallback((option: SeasonOption) => {
    gameRecordService.setManualGameRecordPageState({
      seasonId: option.value,
      page: 1,
      matchId: '',
    });
  }, []);

  useEffect(() => {
    if (seasonOptions?.length && gameRecordPageState.seasonId === defaultState.seasonId) {
      gameRecordService.setDefaultGameRecordPageState({
        seasonId: seasonOptions[0]?.value,
        matchMode: matchModeOptions[0].value,
      });
    }
  }, [seasonOptions, matchModeOptions]);

  const handleClickTab = useCallback(
    (item: GameRecordTabModel) => () => {
      gameRecordService.setManualGameRecordPageState({
        tab: item.value,
      });
      gameRecordService.gameRecordTabClicked(appCode, item.title);
    },
    [appCode],
  );

  return {
    tabs,
    currentTab: gameRecordPageState?.tab,
    handleClickTab,
    seasonOptions,
    season: gameRecordPageState?.seasonId,
    onChangeSeason,
    onChangeMatchMode,
    matchMode: gameRecordPageState?.matchMode,
    matchModeOptions,
    gameRecordDetailOpen: gameRecordPageState?.tab === GameRecordTab.GameRecord && !!gameRecordPageState.matchId,
    userData,
    showLoginMask,
    isLoading,
    channel: configData?.Channel,
  };
}

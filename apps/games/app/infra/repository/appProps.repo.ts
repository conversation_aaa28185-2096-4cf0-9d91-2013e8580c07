import type { Observable } from 'rxjs';
import type { IAppPropsRepo } from '#domains/infra/appProps.repo';
import { BSRepository, Repository } from '@banshee/ex-banshee';
import { BehaviorSubject } from 'rxjs';

// 微服务传入子应用的参数
@Repository('AppPropsRepo')
export class AppPropsRepo extends BSRepository implements IAppPropsRepo {
  private appProps$: BehaviorSubject<any>;

  constructor() {
    super();
    this.appProps$ = new BehaviorSubject(null);
    // this.getAppProps();
  }

  appProps<T>() {
    return this.appProps$.asObservable() as Observable<T>;
  }

  // getAppProps() {
  //   const wujieBus = (window as any)?.$wujie?.bus;
  //   const wujieProps = (window as any).$wujie?.props;
  //   const initSource = of(wujieProps?.subAppProps); // 初始化数据流
  //   const busSource = fromEventPattern(
  //     handler => {
  //       // url参数改变时的数据流
  //       wujieBus.$on('CHANGE_SUB_APP_PROPS', handler);
  //     },
  //     handler => {
  //       wujieBus.$off('CHANGE_SUB_APP_PROPS', handler);
  //     },
  //   ).pipe(map(item => item?.params));
  //   merge(initSource, busSource).subscribe(params => {
  //     this.appProps$.next(params);
  //   });
  // }
}

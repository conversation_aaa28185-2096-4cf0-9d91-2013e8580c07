import type { LooksRank } from '../models/looksRank.model';
import { useCallback, useEffect, useRef, useState } from 'preact/hooks';
import { useService, useStream } from '@banshee/banshee-preact';
import { Gender } from '@ssglauncher/server-api';
import { useCallbackRef } from '@ssglauncher/hooks';
import { useUpdateEffect } from 'ahooks';
import { GameZoneTracking } from '@ssglauncher/tracking';
import { getLooksRank } from '../repositories/looksRank.repo';
import { MbServerListService } from '#domains/zone/service/mbServerList.service';
import { logger } from '#common/utils/logger';
import { useGameCode } from '#common/hooks/useGameCode';
import { useWindowActivated } from '#common/hooks/useWindowActivated';

function useActiveInterval(fn: () => void, delay: number, options?: { onDeactivate(): void; onActivate(): void }) {
  const windowActivated = useWindowActivated();
  const timerCallback = useCallbackRef(fn);
  const timerRef = useRef<ReturnType<typeof setInterval> | null>(null);
  const onActivate = useCallbackRef(options?.onActivate);
  const onDeactivate = useCallbackRef(options?.onDeactivate);

  const clear = useCallback(() => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  }, []);

  useEffect(() => {
    if (!windowActivated) return clear();
    if (!delay) return;

    timerRef.current = setInterval(timerCallback, delay);
    return clear;
  }, [delay, windowActivated]);

  useUpdateEffect(() => {
    if (windowActivated) {
      onActivate();
    } else {
      onDeactivate();
    }
  }, [windowActivated]);
}

export function useLooksRankService() {
  const appCode = useGameCode();
  const mbServerListService = useService(MbServerListService);
  useStream(() => mbServerListService.fetchMbServerList(appCode));
  const selectedServer = useStream(() => mbServerListService.getSelectedServer());
  const [gender, setGender] = useState<Gender>(Gender.Male);
  const [looksRankData, setLooksRankData] = useState<LooksRank[]>([]);
  const [isLoading, setLoading] = useState(true);
  const [current, setCurrent] = useState(0);
  const tracking = useRef(new GameZoneTracking(p => window.xcef.dataAnalysisModule.report(p)));

  const onTabChange = (value: Gender) => {
    setGender(value);
    tracking.current.ssgClickRankPage({
      navListCat: appCode,
      title: value === Gender.Male ? '男性造型' : '女性造型',
    });
  };

  const onCurrentChange = (index: number) => {
    setCurrent(index);
    tracking.current.ssgClickRankDetails({
      navListCat: appCode,
      title: gender === Gender.Male ? '男性造型' : '女性造型',
      Number: index + 1, // 排行榜序号
    });
  };

  const fetchData = useCallback(async () => {
    if (!selectedServer?.key) {
      setLooksRankData([]);
      return;
    }

    setLoading(true);
    try {
      setLooksRankData(await getLooksRank({ server: selectedServer?.key, gender }));
    } catch (e) {
      logger.tag('LooksRank', 'getLooksRank').log(e);
      setLooksRankData([]);
    }
    setLoading(false);
  }, [gender, selectedServer?.key]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  useActiveInterval(fetchData, 300_000, {
    onActivate: fetchData, // refetch data
    onDeactivate: () => setLoading(true), // reset loading
  });

  useEffect(() => {
    setCurrent(0);
  }, [looksRankData]);

  return {
    looksRankData,
    gender,
    isLoading,
    onTabChange,
    current,
    onCurrentChange,
  };
}

import type { BansheeOptions } from '@banshee/ex-banshee';
import type { BSAdaptorConfig } from '@ssglauncher/utils';
import { BSCefAdaptor, disconnectedInterceptor } from '@ssglauncher/utils';

import { Banshee } from '@banshee/ex-banshee';
import { ReduxDevtoolsPlugin } from '@banshee/redux-devtools';
import { XCefSDKPlugin } from '#domains/infra/xcefSdk.plugin';
import { BannerRepo } from '#app/zone/infra/banner.repo';
import { GameConfigRepo } from '#app/zone/infra/gameConfig.repo';
import { ExtensionCardRepo } from '#app/zone/infra/extensionCard.repo';
import { InformationRepo } from '#app/zone/infra/information.repo';
import { EventPosterRepo } from '#app/zone/infra/eventPoster.repo';
import { NewsFeedRepo } from '#app/zone/infra/newsFeed.repo';
import { NotificationRepo } from '#app/zone/infra/notification.repo';
import { ContactRepo } from '#app/zone/infra/contact.repo';
import { ShortcutRepo } from '#app/zone/infra/shortcut.repo';
import { GPNavigatorService } from '#app/zone/infra/gpNavigator.service';
import { SSGNavigatorService } from '#app/zone/infra/ssgNavigator.service';
import { ZoneService } from '#domains/zone/service/zone.service';
import { ZoneUniversalRepo } from '#app/zone/infra/zoneUniversal.repo';
import { GameRecordCardRepo } from '#app/zone/infra/gameRecordCard.repo';
import { GameRecordDataRepo } from '#app/zone/infra/gameRecordData.repo';
import { GameRecordService } from '#domains/zone/service/gameRecord.service';
import { ZoneTrackingService } from '#domains/tracking/service/zoneTracking.service';
import { ChannelRepo } from '#app/infra/repository/channel.repo';
import { ChannelService } from '#domains/channel/service/channel.service';
import { GameRecordTrackingService } from '#domains/tracking/service/gameRecordTracking.service';
import { UserService } from '#domains/account/service/user.service';
import { AccountRepo } from '#app/infra/account.repo';
import { OperatorsRepo } from '#app/infra/operators.repo';
import { AppPropsRepo } from '#app/infra/repository/appProps.repo';
import { getAppEnv } from '#common/utils/appEnv';
import { isGP } from '#common/utils/env';
import { MbServerListRepo } from '#app/zone/infra/mbServerList.repo';
import { MbServerListService } from '#domains/zone/service/mbServerList.service';
import { timeoutInterceptor } from '#common/utils/time.interceptor';

export function createBanshee() {
  const plugins: BansheeOptions['plugins'] = [XCefSDKPlugin];

  if (import.meta.env.DEV) {
    plugins.push(ReduxDevtoolsPlugin);
  }

  const config: BSAdaptorConfig = {
    baseUrl: getAppEnv()['SSG_API_URL'],
    interceptors: {
      response: [disconnectedInterceptor, timeoutInterceptor],
    },
  };

  // banshee
  const banshee = new Banshee({
    plugins,
    providers: [
      // channel
      ChannelRepo,
      ChannelService,

      ZoneUniversalRepo,
      BannerRepo,
      GameConfigRepo,
      InformationRepo,
      EventPosterRepo,
      NewsFeedRepo,
      NotificationRepo,
      ContactRepo,
      ShortcutRepo,
      isGP() ? GPNavigatorService : SSGNavigatorService,
      ZoneService,
      ZoneTrackingService,
      GameRecordTrackingService,
      AppPropsRepo,
      ExtensionCardRepo,
      GameRecordCardRepo,
      GameRecordDataRepo,
      GameRecordService,
      OperatorsRepo,
      AccountRepo,
      UserService,
      MbServerListRepo,
      MbServerListService,
    ],
    request: {
      adaptor: BSCefAdaptor,
      config,
    },
  });

  return banshee;
}

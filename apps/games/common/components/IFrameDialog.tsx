import clsx from 'clsx';
import { createPortal, type ReactNode } from 'preact/compat';
import { useTranslation } from 'react-i18next';
import { useRef, useState, useEffect, useCallback } from 'preact/hooks';
import { SubAppLoading } from './SubAppLoading';

/**
 * IFrame对话框组件属性接口
 */
export interface IFrameDialogProps {
  /** 对话框容器样式类名 */
  className?: string;
  /** 遮罩层样式类名 */
  overlayClassName?: string;
  /** 对话框标题 */
  title?: ReactNode;
  /** 是否显示对话框 */
  open?: boolean;
  /** IFrame源地址 */
  src?: string;
  /** IFrame宽度，默认800px */
  width?: string;
  /** IFrame高度，默认600px */
  height?: string;
  /** 加载超时时间（毫秒），默认30秒 */
  timeout?: number;
  /** 关闭回调函数 */
  onClose?(): void;
  /** 加载成功回调函数 */
  onLoad?(): void;
  /** 加载失败回调函数 */
  onError?(error: Error): void;
}

/**
 * IFrame对话框组件
 * 支持加载状态、错误处理和超时检测的模态对话框
 *
 * 功能特性:
 * - 模态对话框展示IFrame内容
 * - 自动加载状态管理
 * - 错误状态处理和重试
 * - 超时检测机制
 * - 响应式尺寸设置
 * - 完整的生命周期回调
 */
export function IFrameDialog({
  className,
  overlayClassName,
  title,
  open,
  src,
  width = '800px',
  height = '600px',
  timeout = 30000,
  onClose,
  onLoad,
  onError,
}: IFrameDialogProps) {
  const [t] = useTranslation();

  // IFrame元素引用
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // 超时定时器引用
  const timeoutRef = useRef<NodeJS.Timeout>();

  // 组件挂载状态引用
  const isMountedRef = useRef(true);

  // 组件状态管理
  const [isLoading, setIsLoading] = useState(true);
  const [isError, setIsError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  /**
   * 清除超时定时器
   */
  const clearLoadTimeout = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = undefined;
    }
  }, []);

  /**
   * 设置错误状态
   * @param error - 错误对象
   */
  const setErrorState = useCallback(
    (error: Error) => {
      if (!isMountedRef.current) return;

      console.error('IFrame加载失败:', error.message, 'URL:', src);

      clearLoadTimeout();
      setIsLoading(false);
      setIsError(true);
      setErrorMessage(error.message);

      // 调用错误回调
      onError?.(error);
    },
    [src, clearLoadTimeout, onError],
  );

  /**
   * IFrame加载成功处理函数
   */
  const handleIFrameLoad = useCallback(() => {
    if (!isMountedRef.current) return;

    // 清除超时定时器
    clearLoadTimeout();

    // 更新状态
    setIsLoading(false);
    setIsError(false);
    setErrorMessage('');

    console.log('IFrame加载成功:', src);

    // 调用加载成功回调
    onLoad?.();
  }, [src, clearLoadTimeout, onLoad]);

  /**
   * IFrame加载错误处理函数
   */
  const handleIFrameError = useCallback(() => {
    setErrorState(new Error('IFrame加载失败'));
  }, [setErrorState]);

  /**
   * 重试加载函数
   */
  const handleRetry = useCallback(() => {
    if (!src || !iframeRef.current) return;

    setIsLoading(true);
    setIsError(false);
    setErrorMessage('');
    clearLoadTimeout();

    // 重新设置IFrame源地址触发重新加载
    iframeRef.current.src = src;

    // 设置新的超时定时器
    timeoutRef.current = setTimeout(() => {
      if (isMountedRef.current && isLoading) {
        setErrorState(new Error('IFrame加载超时'));
      }
    }, timeout);
  }, [src, timeout, isLoading, clearLoadTimeout, setErrorState]);

  /**
   * 关闭对话框处理函数
   */
  const handleClose = useCallback(() => {
    clearLoadTimeout();
    onClose?.();
  }, [clearLoadTimeout, onClose]);

  // 当src变化时重新加载
  useEffect(() => {
    if (!open || !src) return;

    setIsLoading(true);
    setIsError(false);
    setErrorMessage('');
    clearLoadTimeout();

    // 设置加载超时
    timeoutRef.current = setTimeout(() => {
      if (isMountedRef.current && isLoading) {
        setErrorState(new Error('IFrame加载超时'));
      }
    }, timeout);

    console.log('开始加载IFrame:', src);
  }, [open, src, timeout, isLoading, clearLoadTimeout, setErrorState]);

  // 组件卸载清理
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      clearLoadTimeout();
    };
  }, [clearLoadTimeout]);

  // 对话框未打开时不渲染
  if (!open) return null;

  /**
   * 渲染加载状态
   */
  const renderLoading = () => {
    if (!isLoading) return null;

    return (
      <div className="absolute inset-0 flex items-center justify-center bg-black-100/80 z-10">
        <div className="flex flex-col items-center gap-4">
          <SubAppLoading />
          <p className="text-white/60 text-3.5/5">正在加载内容...</p>
        </div>
      </div>
    );
  };

  /**
   * 渲染错误状态
   */
  const renderError = () => {
    if (!isError) return null;

    return (
      <div className="absolute inset-0 flex items-center justify-center bg-black-100/80 z-10">
        <div className="flex flex-col items-center gap-4 px-8 text-center">
          <div className="w-12 h-12 flex items-center justify-center bg-red-500/20 rounded-full">
            <svg className="w-6 h-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>
          <div>
            <h3 className="text-white/90 text-4/5.5 font-medium mb-2">加载失败</h3>
            <p className="text-white/60 text-3.5/5 mb-4">{errorMessage || '无法加载内容，请检查网络连接'}</p>
          </div>
          <button
            className="min-w-108px h-8 px-4 flex items-center justify-center text-3.5/5 text-white/90 border border-mbBrand/60 bg-mbBrand/20 hover:border-mbBrand/80 hover:bg-mbBrand/30 active:border-mbBrand/60 active:bg-mbBrand/20"
            onClick={handleRetry}
          >
            重试
          </button>
        </div>
      </div>
    );
  };

  /**
   * 渲染IFrame内容
   */
  const renderIFrame = () => {
    if (!src) return null;

    return (
      <iframe
        ref={iframeRef}
        src={src}
        className="w-full h-full border-0"
        onLoad={handleIFrameLoad}
        onError={handleIFrameError}
        sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-modals allow-top-navigation"
        title={typeof title === 'string' ? title : 'IFrame Dialog'}
      />
    );
  };

  return createPortal(
    <div className={clsx(overlayClassName, 'fixed top-0 left-0 h-full w-full flex items-center justify-center z-9999')}>
      {/* 遮罩层 */}
      <div className="backdrop absolute inset-0 z-0" onClick={handleClose} />

      {/* 对话框主体 */}
      <div
        className={clsx(
          className,
          'flex flex-col pt-6 pb-5 bg-black-100 border border-mbBrand/60 z-1 shadow-modal animate-fadeIn',
        )}
        style={{ width, height }}
      >
        {/* 对话框头部 */}
        <header
          className={clsx(
            'shrink-0 mb-3.5 px-7 flex items-center justify-between',
            'before:content-[""] before:block before:w-0.5 before:h-4.5 before:shrink-0 before:mb-0.5 before:mr-1 before:bg-mbBrand',
          )}
        >
          <h1 className="font-bold text-4/5.5 text-white/90">{title}</h1>

          {/* 关闭按钮 */}
          <button
            className="w-6 h-6 flex items-center justify-center text-white/60 hover:text-white/90 hover:bg-white/10 rounded"
            onClick={handleClose}
            title="关闭"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </header>

        {/* 对话框内容区域 */}
        <main className="relative flex-1 min-h-0 mx-7 mb-4 border border-white/10 bg-white/5">
          {renderIFrame()}
          {renderLoading()}
          {renderError()}
        </main>

        {/* 对话框底部 */}
        <footer className="shrink-0 px-7 flex justify-end items-center gap-2">
          <button
            className="min-w-108px h-8 px-1 flex items-center justify-center text-3.5/5 text-white/80 border border-white/8 bg-white/8 hover:border-white/20 hover:bg-white/20 active:border-white/12 active:bg-white/12"
            onClick={handleClose}
          >
            {t('common:CLOSE', '关闭')}
          </button>
        </footer>
      </div>
    </div>,
    document.body,
  );
}

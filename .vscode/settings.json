{
  "unocss.root": [
    // "apps/main",
    // "apps/games",
    // "apps/additional",
    // "libs/components"
  ],
  "cSpell.words": [
    "Disp",
    "Evaculate",
    "iconfont",
    "MASHMARK",
    "mecha",
    "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    "Msmk",
    "Shatterer",
    "ssglauncher",
    "wujie",
    "xcef"
  ],
  "typescript.tsdk": "node_modules/typescript/lib",
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "[svg]": {
    "editor.defaultFormatter": "jock.svg"
  },
  "kiroAgent.configureMCP": "Disabled",
  "[typescriptreact]": {
    "editor.defaultFormatter": "vscode.typescript-language-features"
  }
}

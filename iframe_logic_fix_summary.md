# IFrame 逻辑异常修复总结

## 问题描述
在多个 `SubAppIFrame.tsx` 文件中存在逻辑异常：即使 `handleIFrameLoad` 成功执行，`handleIFrameError` 仍可能被调用，导致加载成功后仍显示错误状态。

## 根本原因
1. **超时定时器未清除**：在 `handleIFrameLoad` 中没有清除 `loadTimeoutRef.current` 定时器
2. **竞态条件**：30秒超时定时器可能在 iframe 加载成功后仍然触发
3. **状态管理不一致**：某些文件缺少 `isDestroyedRef` 检查

## 修复的文件

### 1. `apps/main/app/subappManager/view/SubAppIFrame.tsx`
**问题**：
- `handleIFrameLoad` 中没有清除超时定时器
- `handleIFrameError` 中没有清除超时定时器

**修复**：
```typescript
const handleIFrameLoad = useCallback(() => {
  // 清除超时定时器，防止加载成功后仍触发超时错误
  // Clear timeout to prevent timeout error after successful load
  if (loadTimeoutRef.current) {
    clearTimeout(loadTimeoutRef.current);
    loadTimeoutRef.current = undefined;
  }
  
  // ... 其余逻辑
}, [onActivated]);

const handleIFrameError = useCallback(() => {
  // 清除超时定时器，因为已经发生错误
  // Clear timeout since error has occurred
  if (loadTimeoutRef.current) {
    clearTimeout(loadTimeoutRef.current);
    loadTimeoutRef.current = undefined;
  }
  
  // ... 其余逻辑
}, [app.url, loadError]);
```

### 2. `apps/main/app/subappManager/view/IFrameSubApp/SubAppIFrame.tsx`
**问题**：
- 缺少 `isDestroyedRef` 状态管理
- `handleIFrameLoad` 和 `handleIFrameError` 中没有清除超时定时器

**修复**：
- 添加 `isDestroyedRef` 状态管理
- 在两个处理函数中添加超时定时器清除逻辑
- 在 `start` 函数中添加清除之前定时器的逻辑

### 3. `apps/main/app/subappManager/view/IFrameViewer/SubAppIFrame.tsx`
**状态**：✅ 已正确实现
- 正确清除了超时定时器
- 状态管理完善

## 修复后的逻辑流程

1. **开始加载**：
   - 清除之前的超时定时器
   - 设置新的30秒超时定时器
   - 设置 `isLoading = true`

2. **加载成功** (`handleIFrameLoad`)：
   - ✅ 清除超时定时器
   - 设置 `isLoading = false, isError = false`
   - 调用 `onActivated` 回调

3. **加载失败** (`handleIFrameError`)：
   - ✅ 清除超时定时器
   - 设置 `isLoading = false, isError = true`

4. **超时处理**：
   - 只有在 `isLoading = true` 且组件未销毁时才触发错误
   - 如果已经加载成功或失败，定时器已被清除，不会重复触发

5. **组件销毁**：
   - 设置 `isDestroyedRef.current = true`
   - 清除所有定时器
   - 调用 `onDeactivated` 回调

## 关键改进

1. **防止竞态条件**：在成功和失败回调中都清除超时定时器
2. **状态一致性**：使用 `isDestroyedRef` 防止组件销毁后的状态更新
3. **资源清理**：确保所有定时器都被正确清除
4. **中英文注释**：添加详细的中英文注释说明修复逻辑

## 测试建议

1. **正常加载**：验证 iframe 正常加载时不会触发错误状态
2. **网络错误**：验证网络错误时正确显示错误状态
3. **超时场景**：验证30秒超时后正确显示错误状态
4. **快速切换**：验证快速切换 URL 时状态管理正确
5. **组件销毁**：验证组件销毁时资源正确清理

## 修复文件位置

- `apps/main/app/subappManager/view/SubAppIFrame_fixed.tsx`
- `apps/main/app/subappManager/view/IFrameSubApp/SubAppIFrame_fixed.tsx`

请将修复后的文件替换原文件以解决逻辑异常问题。
